import { useEffect, useMemo, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import AccountBoxRoundedIcon from '@mui/icons-material/AccountBoxRounded';
import AccountTreeRoundedIcon from '@mui/icons-material/AccountTreeRounded';
import AddtoPhotosRoundedIcon from '@mui/icons-material/AddToPhotosRounded';
import ChevronRight from '@mui/icons-material/ChevronRight';
import DeleteForeverIcon from '@mui/icons-material/DeleteForeverRounded';
import EventAvailableRoundedIcon from '@mui/icons-material/EventAvailableRounded';
import HelpOutline from '@mui/icons-material/HelpOutline';
import InboxRoundedIcon from '@mui/icons-material/InboxRounded';
import InsertDriveFileRoundedIcon from '@mui/icons-material/InsertDriveFileRounded';
import ListRoundedIcon from '@mui/icons-material/ListRounded';
import MonetizationOnRoundedIcon from '@mui/icons-material/MonetizationOnRounded';
import NotificationsRoundedIcon from '@mui/icons-material/NotificationsRounded';
import PeopleRoundedIcon from '@mui/icons-material/PeopleRounded';
import RedeemIcon from '@mui/icons-material/Redeem';
import RocketRoundedIcon from '@mui/icons-material/RocketRounded';
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded';
import ShieldRoundedIcon from '@mui/icons-material/ShieldRounded';
import WorkRoundedIcon from '@mui/icons-material/WorkRounded';
import WorkspacePremiumRoundedIcon from '@mui/icons-material/WorkspacePremiumRounded';
import { alpha, Box, Button, Grid, Stack, Typography, useTheme } from '@mui/material';
import { differenceInDays } from 'date-fns';

import { isIosShell } from '@carepatron/utilities';

import AppNotificationsPanel from 'components/AppNotificationsPanel';
import StandardAvatar from 'components/common/avatar/StandardAvatar';
import { intl } from 'lang';
import langIds from 'lang/langIds';
import useTranscriptionsQueueWatcher from 'services/transcriptions/hooks/useTranscriptionsQueueWatcher';
import { selectBillingAccountFromResult } from 'store/api/billing/selectors';
import { useGetBillingAccountQuery } from 'store/api/billing/service';
import { useCurrentLayoutRoute } from 'store/api/onboarding/hooks';
import { useCurrentUserPermissions } from 'store/api/permissions/hooks';
import { useCurrentProvider } from 'store/api/providers/hooks';
import { useGetReferralCodeQuery } from 'store/api/referrals/hooks';
import { selectStaffHexColorFromResult } from 'store/api/staff/selectors';
import { useGetOneStaffQuery } from 'store/api/staff/service';
import { selectUserEntity } from 'store/api/user/selectors';
import { useLegacyFeatureModule } from 'store/slices/features/hooks';
import { addModal } from 'store/slices/modals/slice';
import { selectNavigationDrawerShrunk } from 'store/slices/navigationDrawer/selectors';
import { selectIsCurrentUserProvider } from 'store/slices/staff/selectors';
import { selectActiveSubNavigationDrawerId } from 'store/slices/subNavigationDrawer/selectors';
import { openSubNavigationDrawer } from 'store/slices/subNavigationDrawer/slice';
import { QUERY_KEYS } from 'util/constants';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { useNavigationTracking } from 'util/hooks/useNavigationTracking';
import useShowCompleteSetup from 'util/hooks/useShowCompleteSetup';
import { trackNotificationEvent } from 'util/notifications';
import { toFullName } from 'util/person';

import DrawerNavItem from '../DrawerNavItem';
import CompleteSetupButton from '../GlobalHeader/components/CompleteSetupButton';

import BookOnboardingCallButton from './BookOnboardingCallButton';
import { TruncateText } from './styled';
import { MenuItem, MenuProps } from './types';

type UpgradeButtonProps = {
	isShrunk: boolean;
	showDiscount?: boolean;
	onClick: () => void;
};

export const UpgradeButton = ({ onClick, isShrunk, showDiscount = false }: UpgradeButtonProps) => {
	if (isIosShell()) return null;
	return (
		<>
			<Button onClick={onClick} fullWidth color='primary' variant='contained'>
				<Stack direction='row' alignItems='center' spacing={1}>
					{showDiscount ? (
						<>
							<Box>🔥</Box>
							{!isShrunk && (
								<Typography
									sx={{ color: (theme) => theme.palette.primary.contrastText }}
									variant='subtitle2'
								>
									<FormattedMessage id={langIds.Get50PercentOff} />
								</Typography>
							)}
						</>
					) : (
						<>
							<RocketRoundedIcon sx={{ width: '20px' }} />
							{!isShrunk && (
								<Typography
									sx={{ color: (theme) => theme.palette.primary.contrastText }}
									variant='subtitle2'
								>
									<FormattedMessage id={langIds.UpgradeNow} />
								</Typography>
							)}
						</>
					)}
				</Stack>
			</Button>
		</>
	);
};

type ProviderMenuReturnProps = {
	menuItems: MenuItem[];
};

export const useProviderMenu = (): ProviderMenuReturnProps => {
	const isSmallScreen = useBreakpoint('md');
	const { permissions } = useCurrentUserPermissions();
	const hasInsuranceBillingModule = useLegacyFeatureModule('InsuranceBilling');
	const isGlobalHeaderShown = !isSmallScreen;

	const menuItems = useMemo(
		() =>
			[
				permissions.schedulingView !== 'None' && {
					key: 'Calendar',
					label: langIds.HeaderCalendar,
					icon: EventAvailableRoundedIcon,
					route: 'Calendar',
				},
				!isGlobalHeaderShown && {
					key: 'Activity',
					label: langIds.Activity,
					icon: NotificationsRoundedIcon,
				},
				{
					key: 'Inbox',
					label: langIds.Inbox,
					icon: InboxRoundedIcon,
					route: 'Inbox',
				},
				permissions.clientProfilesView !== 'None' && {
					key: 'Clients',
					label: langIds.HeaderClients,
					icon: PeopleRoundedIcon,
					route: 'Clients',
				},
				permissions.invoicesView !== 'None' && {
					key: 'Billing',
					label: langIds.NavigationDrawerInvoices,
					icon: MonetizationOnRoundedIcon,
					route: 'Billing',
				},
				permissions.workspaceSettingsView === 'Everything' && {
					key: 'Staff',
					label: langIds.YourTeam,
					icon: WorkRoundedIcon,
					route: 'Staff',
				},
				permissions.clientProfilesView !== 'None' && {
					key: 'Contacts',
					label: langIds.Contacts,
					icon: ListRoundedIcon,
					route: 'Contacts',
				},
				{
					key: 'Templates',
					label: langIds.NavigationDrawerTemplates,
					icon: InsertDriveFileRoundedIcon,
					route: 'Templates',
				},
				{
					key: 'Settings',
					label: langIds.NavigationDrawerProviderSettings,
					icon: SettingsRoundedIcon,
					route: 'Settings',
					submenu: {
						label: langIds.NavigationDrawerProviderSettings,
						items: [
							{
								key: 'SettingsPersonal',
								label: langIds.NavigationDrawerPersonal,
								description: langIds.NavigationDrawerPersonalInfo,
								icon: AccountBoxRoundedIcon,
								route: 'Settings/Personal/Details',
							},
							permissions.workspaceSettingsView === 'Everything' && {
								key: 'SettingsWorkspace',
								label: langIds.NavigationDrawerWorkspace,
								description: langIds.NavigationDrawerWorkspaceInfo,
								icon: WorkspacePremiumRoundedIcon,
								route: 'Settings/Workspace/Details',
							},
							permissions.workspaceSettingsView === 'Everything' && {
								key: 'SettingsBilling',
								label: langIds.NavigationDrawerBilling,
								description: langIds.NavigationDrawerBillingInfo,
								icon: MonetizationOnRoundedIcon,
								route: 'Settings/Billing/Details',
							},
							hasInsuranceBillingModule &&
								permissions.workspaceSettingsView === 'Everything' && {
									key: 'SettingsInsurance',
									label: langIds.NavigationDrawerInsurance,
									description: langIds.NavigationDrawerInsuranceInfo,
									icon: ShieldRoundedIcon,
									route: 'Settings/Insurance/Payers',
								},
							permissions.workspaceSettingsView === 'Everything' && {
								key: 'SettingsSchedule',
								label: langIds.NavigationDrawerScheduling,
								description: langIds.NavigationDrawerSchedulingInfo,
								icon: AddtoPhotosRoundedIcon,
								route: 'Settings/Scheduling/Locations',
							},
							permissions.workspaceSettingsView === 'Everything' && {
								key: 'SettingsWorkflow',
								label: langIds.WorkflowsManagement,
								description: langIds.AutomationAndReminders,
								icon: AccountTreeRoundedIcon,
								route: 'Settings/Workflow',
							},

							'-',

							permissions.workspaceSettingsView === 'Everything' && {
								key: 'Trash',
								label: langIds.NavigationDrawerTrash,
								description: langIds.NavigationDrawerTrashInfo,
								icon: DeleteForeverIcon,
								route: 'Trash',
							},
						].filter(Boolean),
					},
				},
			].filter(Boolean) as MenuItem[],
		[
			permissions.schedulingView,
			permissions.clientProfilesView,
			permissions.invoicesView,
			permissions.workspaceSettingsView,
			hasInsuranceBillingModule,
			isGlobalHeaderShown,
		]
	);

	return {
		menuItems,
	};
};

const getSubmenusWithoutSeparator = (submenuItems) => {
	return submenuItems?.filter((submenuItem) => submenuItem !== '-') ?? [];
};

const ProviderMenu: React.FC<MenuProps> = ({ navigate, openHelp, onOpenProfile }) => {
	const theme = useTheme();
	const isMobile = useBreakpoint('sm');
	const isSmallScreen = useBreakpoint('md');
	const dispatch = useDispatch();
	const [searchParams] = useSearchParams();
	const { trackNavigation } = useNavigationTracking();
	const [activityAnchorEl, setActivityAnchorEl] = useState<HTMLElement | null>(null);
	const { showCompleteSetupButton, onCompleteSetup } = useShowCompleteSetup();

	// TODO - find out where to better put this
	useTranscriptionsQueueWatcher();

	const isGlobalHeaderShown = !isSmallScreen;

	const { menuItems } = useProviderMenu();

	const referAFriend = searchParams.get(QUERY_KEYS.referAFriend);

	const currentUser = useSelector(selectUserEntity);
	const { permissions } = useCurrentUserPermissions();
	const { currentProvider } = useCurrentProvider();
	const isShrunk = useSelector(selectNavigationDrawerShrunk);
	const isProviderUser = useSelector(selectIsCurrentUserProvider);
	const {
		billingAccount: { billingAccountType, isFirstTimeSubscriber, trialEndDateUtc },
	} = useGetBillingAccountQuery(
		{},
		{
			selectFromResult: (result) => ({
				...result,
				billingAccount: selectBillingAccountFromResult(result),
			}),
			skip: !currentUser || !currentProvider,
		}
	);
	const { data: referralData } = useGetReferralCodeQuery({});

	const { userColor } = useGetOneStaffQuery(
		{ id: currentUser?.id! },
		{
			skip: !currentUser?.id,
			selectFromResult: (result) => ({
				userColor: selectStaffHexColorFromResult(result),
			}),
		}
	);
	const currentRoute = useCurrentLayoutRoute();

	const { firstName = '', lastName = '' } = currentUser ?? {};
	const username = toFullName(firstName, lastName);

	const activeSubNavigationDrawerId = useSelector(selectActiveSubNavigationDrawerId);

	const isOnboardingCallButtonShown = useMemo(() => {
		if (!isProviderUser) return false;

		const signedUpAt = currentUser?.createdDateTimeUtc;
		const daysSinceSignUp = signedUpAt ? differenceInDays(new Date(), new Date(signedUpAt)) : 0;
		if (daysSinceSignUp > 60) return false;

		return true;
	}, [currentUser?.createdDateTimeUtc, isProviderUser]);

	// has correct permission and the account is on free plan or trial
	const isUpgradeButtonShown =
		isProviderUser &&
		permissions.workspaceSettingsEdit === 'Everything' &&
		(billingAccountType === 'Free' || Boolean(trialEndDateUtc));
	const isEarnNavItemShown = isProviderUser && !!referralData?.reward && !isIosShell();

	useEffect(() => {
		if (referAFriend === 'true') {
			dispatch(addModal({ type: 'ManageReferrals' }));
		}
	}, [dispatch, referAFriend]);

	return (
		<>
			{menuItems.map((item) => {
				const handleItemClick = (evt: React.MouseEvent<HTMLElement>) => {
					const submenusWithoutSeparator = getSubmenusWithoutSeparator(item.submenu?.items);
					const hasSingleSubmenuItem = submenusWithoutSeparator?.length === 1;

					// For some special menu items
					if (item.key === 'Activity') {
						trackNotificationEvent('open in-app notifications panel');
						setActivityAnchorEl(evt.currentTarget);
						return;
					}

					// For normal menu
					if (!item.submenu || hasSingleSubmenuItem) {
						const [firstSubmenuItem] = submenusWithoutSeparator ?? [];
						const menuRoute =
							firstSubmenuItem !== '-' ? (firstSubmenuItem?.route ?? item.route) : item.route;
						trackNavigation(menuRoute);
						navigate(menuRoute);
						return;
					}

					// For sub-menu
					dispatch(
						openSubNavigationDrawer({
							id: item.label,
							submenu: item.submenu,
						})
					);
				};

				const isSelected = (() => {
					if (item.key === 'Activity') {
						return Boolean(activityAnchorEl);
					}

					return !!activeSubNavigationDrawerId
						? activeSubNavigationDrawerId === item.label
						: currentRoute === item.route;
				})();

				return (
					<DrawerNavItem
						key={item.key}
						menuKey={item.key}
						label={item.label}
						text={<FormattedMessage id={item.label} />}
						hasSecondaryActions
						Icon={item.icon}
						isSelected={isSelected}
						onClick={handleItemClick}
						isShrunk={isShrunk}
						data-menu-id={item.label}
						aria-label={intl.formatMessage({ id: item.label })}
					/>
				);
			})}
			{/* When global header is shown, these items would be placed on global nav instead */}
			{!isGlobalHeaderShown && (
				<>
					{!isIosShell() && (
						<DrawerNavItem
							text={<FormattedMessage id={langIds.HeaderHelp} />}
							Icon={HelpOutline}
							isSelected={false}
							onClick={openHelp}
							isShrunk={isShrunk}
						/>
					)}
					{isEarnNavItemShown && (
						<DrawerNavItem
							text={
								<FormattedMessage
									id={langIds.EarnReferralCredit}
									values={{ creditAmount: referralData.reward!.creditAmount }}
								/>
							}
							hasSecondaryActions
							Icon={RedeemIcon}
							isShrunk={isShrunk}
							isSelected={false}
							onClick={() => dispatch(addModal({ type: 'ManageReferrals' }))}
						/>
					)}
					{showCompleteSetupButton && isMobile && (
						<Box sx={{ px: isShrunk ? 1 : 2, pt: 1, pb: isOnboardingCallButtonShown ? 1 : 0 }}>
							<CompleteSetupButton onClose={onCompleteSetup} />
						</Box>
					)}
				</>
			)}

			<Box sx={{ mt: 'auto', mb: 2 }}>
				{isOnboardingCallButtonShown && (
					<Box
						sx={{
							px: isShrunk ? 1 : 2,
							alignItems: 'center',
						}}
					>
						<BookOnboardingCallButton isShrunk={isShrunk} />
					</Box>
				)}
				{isUpgradeButtonShown && (
					<Box
						sx={{
							px: isShrunk ? 1 : 2,
							py: 2,
							mb: !isGlobalHeaderShown ? 2 : 0,
							alignItems: 'center',
						}}
					>
						<UpgradeButton
							isShrunk={isShrunk}
							showDiscount={isFirstTimeSubscriber ?? false}
							onClick={() => navigate('Settings/Workspace/Subscriptions/Manage')}
						/>
					</Box>
				)}
				{!isGlobalHeaderShown && (
					<DrawerNavItem
						text={
							<Grid container justifyContent='space-between' alignItems='center' pr={isShrunk ? 0.5 : 0}>
								<Grid item xs={11}>
									<TruncateText variant='subtitle1' color={theme.palette.common.white}>
										{username}
									</TruncateText>
									<TruncateText
										sx={{
											color: alpha(theme.palette.common.white, 0.53),
										}}
										variant='body2'
									>
										{currentProvider?.name}
									</TruncateText>
								</Grid>
								<Grid item xs={1} display='flex' justifyItems='center'>
									<ChevronRight />
								</Grid>
							</Grid>
						}
						Icon={() => (
							<StandardAvatar
								testId='nav-avatar'
								component='button'
								id={currentUser?.id}
								profilePhotoId={currentUser!.profilePhotoId}
								firstName={firstName}
								lastName={lastName}
								colorHex={userColor}
							/>
						)}
						isSelected={false}
						onClick={onOpenProfile}
						isShrunk={isShrunk}
						sx={{
							paddingLeft: isShrunk ? 1 : 'auto',
						}}
					/>
				)}
			</Box>

			{/* In-app notifications panel */}
			<AppNotificationsPanel
				open={Boolean(activityAnchorEl)}
				onClose={() => setActivityAnchorEl(null)}
				popoverProps={{
					anchorEl: activityAnchorEl,
					PaperProps: { sx: { ml: 2 } },
				}}
			/>
		</>
	);
};

export default ProviderMenu;
