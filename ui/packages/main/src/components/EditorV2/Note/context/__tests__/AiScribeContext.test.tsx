import React from 'react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { renderHook } from '@testing-library/react';

import { logger } from '@carepatron/utilities';

// Import the component after mocking
import useCompleteTranscriptionParts from 'services/transcriptions/hooks/useCompleteTranscriptionParts';

// Mock dependencies
jest.mock('@carepatron/utilities', () => ({
	logger: {
		warn: jest.fn(),
		error: jest.fn(),
	},
}));

jest.mock('services/transcriptions/hooks/useCompleteTranscriptionParts', () => {
	return jest.fn(() => jest.fn());
});

jest.mock('store/api/transcriptions/hooks', () => ({
	useCompleteTranscriptionPartMutation: jest.fn(() => [jest.fn()]),
	useDeleteTranscriptionMutation: jest.fn(() => [jest.fn()]),
	useDeleteTranscriptionPartMutation: jest.fn(() => [jest.fn()]),
	useRetryTranscriptionMutation: jest.fn(() => [jest.fn()]),
	useStartTranscriptionMutation: jest.fn(() => [jest.fn()]),
	useUploadTranscriptionPartMutation: jest.fn(() => [jest.fn()]),
}));

jest.mock('store/api/notes/hooks', () => ({
	useGetNoteTranscriptionsQuery: jest.fn(() => ({ data: [] })),
}));

jest.mock('store/api/templates/hooks', () => ({
	useGetTemplatesQuery: jest.fn(() => ({ data: [] })),
	useGetPublicTemplatesQuery: jest.fn(() => ({ data: [] })),
}));

jest.mock('store/slices/features/hooks', () => ({
	useFeatureFlag: jest.fn(() => ({ hasFeatureFlagged: false })),
}));

jest.mock('services/transcribe/hooks/useAvailableInputAudioDevices', () => ({
	useAvailableInputAudioDevices: jest.fn(() => ({ data: [] })),
}));

jest.mock('services/transcribe/hooks/useInputAudioPermissions', () => ({
	useInputAudioPermissions: jest.fn(() => ({ permissionStatus: 'granted' })),
}));

describe('AiScribeContext - Stuck Transcription Detection', () => {
	const mockTriggerCompleteParts = jest.fn();
	const mockQueueRef = {
		current: {
			size: 0,
			pending: 0,
		},
	};
	const mockIndexedDBQueue = {
		current: {
			getAllItems: jest.fn(),
		},
	};

	// Create a minimal store for testing
	const createTestStore = () =>
		configureStore({
			reducer: {
				test: (state = {}) => state,
			},
		});

	const wrapper = ({ children }: { children: React.ReactNode }) => (
		<Provider store={createTestStore()}>{children}</Provider>
	);

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();

		// Setup mocks
		(useCompleteTranscriptionParts as jest.Mock).mockReturnValue(mockTriggerCompleteParts);
		mockTriggerCompleteParts.mockResolvedValue(undefined);
		mockIndexedDBQueue.current.getAllItems.mockResolvedValue([]);
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	// Helper function to create a mock transcription
	const createMockTranscription = (overrides = {}) => ({
		id: 'transcription-123',
		status: 'InProgress',
		contactId: 'contact-456',
		noteId: 'note-789',
		createdDateTimeUtc: new Date(Date.now() - 13 * 60 * 60 * 1000).toISOString(), // 13 hours ago
		...overrides,
	});

	// Helper function to create checkAndRecoverStuckTranscription function
	const createCheckAndRecoverFunction = () => {
		const TRANSCRIPTION_STUCK_THRESHOLD_IN_MINUTES = 12 * 60; // 12 hours

		return async (transcription: any) => {
			if (!transcription || !transcription.id) return;

			// Only attempt recovery for InProgress or PendingCompletion status
			if (transcription.status !== 'InProgress' && transcription.status !== 'PendingCompletion') {
				return;
			}

			// Check if transcription has been stuck for a reasonable time
			const createdTime = new Date(transcription.createdDateTimeUtc).getTime();
			const currentTime = Date.now();
			const minutesStuck = (currentTime - createdTime) / (1000 * 60);

			if (minutesStuck < TRANSCRIPTION_STUCK_THRESHOLD_IN_MINUTES) {
				return;
			}

			// Check if queue is idle
			const isQueueIdle = mockQueueRef.current.size === 0 && mockQueueRef.current.pending === 0;

			if (!isQueueIdle) {
				return;
			}

			// Check IndexedDB queue
			try {
				const allQueueItems = await mockIndexedDBQueue.current.getAllItems();
				const transcriptionQueueItems = allQueueItems.filter(
					(item: any) => item.value.transcriptionId === transcription.id
				);

				if (transcriptionQueueItems.length > 0) {
					return;
				}
			} catch (error) {
				logger.error('Error checking IndexedDB queue for stuck transcription', {
					transcriptionId: transcription.id,
					error,
				});
				return;
			}

			try {
				logger.warn('Detected stuck transcription, attempting automatic recovery', {
					transcriptionId: transcription.id,
					status: transcription.status,
					minutesStuck: Math.round(minutesStuck),
					createdTime: transcription.createdDateTimeUtc,
				});

				await mockTriggerCompleteParts({
					transcriptionId: transcription.id,
					contactId: transcription.contactId,
					noteId: transcription.noteId,
				});
			} catch (error) {
				logger.error('Failed to recover stuck transcription', {
					transcriptionId: transcription.id,
					status: transcription.status,
					minutesStuck: Math.round(minutesStuck),
					error,
				});
			}
		};
	};

	describe('checkAndRecoverStuckTranscription', () => {
		it('should not attempt recovery for transcriptions without ID', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			await checkAndRecover(null);
			await checkAndRecover({});
			await checkAndRecover({ id: '' });

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();
		});

		it('should not attempt recovery for transcriptions with wrong status', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			const completedTranscription = createMockTranscription({ status: 'Completed' });
			const failedTranscription = createMockTranscription({ status: 'Failed' });

			await checkAndRecover(completedTranscription);
			await checkAndRecover(failedTranscription);

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();
		});

		it('should not attempt recovery for recent transcriptions', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			// Transcription created 1 hour ago (within 12-hour threshold)
			const recentTranscription = createMockTranscription({
				createdDateTimeUtc: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
			});

			await checkAndRecover(recentTranscription);

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();
		});

		it('should not attempt recovery when queue is not idle', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			// Set queue as not idle
			mockQueueRef.current.size = 1;

			const stuckTranscription = createMockTranscription();

			await checkAndRecover(stuckTranscription);

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();

			// Reset queue state
			mockQueueRef.current.size = 0;
		});

		it('should not attempt recovery when IndexedDB has items for transcription', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			// Mock IndexedDB with items for this transcription
			mockIndexedDBQueue.current.getAllItems.mockResolvedValue([
				{
					value: {
						transcriptionId: 'transcription-123',
						partNumber: 1,
					},
				},
			]);

			const stuckTranscription = createMockTranscription();

			await checkAndRecover(stuckTranscription);

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();
		});

		it('should attempt recovery for stuck InProgress transcription', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			const stuckTranscription = createMockTranscription({
				status: 'InProgress',
			});

			await checkAndRecover(stuckTranscription);

			expect(logger.warn).toHaveBeenCalledWith(
				'Detected stuck transcription, attempting automatic recovery',
				expect.objectContaining({
					transcriptionId: 'transcription-123',
					status: 'InProgress',
				})
			);

			expect(mockTriggerCompleteParts).toHaveBeenCalledWith({
				transcriptionId: 'transcription-123',
				contactId: 'contact-456',
				noteId: 'note-789',
			});
		});

		it('should attempt recovery for stuck PendingCompletion transcription', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			const stuckTranscription = createMockTranscription({
				status: 'PendingCompletion',
			});

			await checkAndRecover(stuckTranscription);

			expect(mockTriggerCompleteParts).toHaveBeenCalledWith({
				transcriptionId: 'transcription-123',
				contactId: 'contact-456',
				noteId: 'note-789',
			});
		});

		it('should handle IndexedDB errors gracefully', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			const dbError = new Error('IndexedDB error');
			mockIndexedDBQueue.current.getAllItems.mockRejectedValue(dbError);

			const stuckTranscription = createMockTranscription();

			await checkAndRecover(stuckTranscription);

			expect(logger.error).toHaveBeenCalledWith(
				'Error checking IndexedDB queue for stuck transcription',
				expect.objectContaining({
					transcriptionId: 'transcription-123',
					error: dbError,
				})
			);

			expect(mockTriggerCompleteParts).not.toHaveBeenCalled();
		});

		it('should handle recovery API errors gracefully', async () => {
			const checkAndRecover = createCheckAndRecoverFunction();

			const apiError = new Error('API error');
			mockTriggerCompleteParts.mockRejectedValue(apiError);

			const stuckTranscription = createMockTranscription();

			await checkAndRecover(stuckTranscription);

			expect(logger.error).toHaveBeenCalledWith(
				'Failed to recover stuck transcription',
				expect.objectContaining({
					transcriptionId: 'transcription-123',
					error: apiError,
				})
			);
		});
	});
});
