import React, { createContext, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import { batch, useSelector } from 'react-redux';
import PQueue from '@esm2cjs/p-queue';
import uuid from 'uuid/v4';

import { logger } from '@carepatron/utilities';

import { useAudioInputRecorder } from 'components/EditorV2/common/hooks/useAudioInputRecorder';
import { SILENCE_CHUNK_COUNT_PER_MINUTE } from 'components/EditorV2/common/hooks/useAudioInputRecorder/constants';
import { POSITION } from 'components/EditorV2/common/modal';
import { hasForms } from 'components/EditorV2/Core/utils/tiptap';
import { useNoteHandlersContext } from 'components/EditorV2/Note/context/NoteHandlersContext';
import { useNoteStateContext } from 'components/EditorV2/Note/context/NoteStateContext';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import api from 'services/api';
import { useAvailableInputAudioDevices } from 'services/transcribe/hooks/useAvailableInputAudioDevices';
import { PermissionStatus, useInputAudioPermissions } from 'services/transcribe/hooks/useInputAudioPermissions';
import useCompleteTranscriptionParts from 'services/transcriptions/hooks/useCompleteTranscriptionParts';
import TranscriptionsQueue from 'services/transcriptions/transcriptionsQueue';
import { TranscriptionStateManager } from 'services/transcriptions/transcriptionStateManager';
import { useAppDispatch } from 'store';
import { createBottomModal, removeBottomModal, updateBottomModal } from 'store/actions/bottomModals';
import { contactNoteFetched } from 'store/actions/contactNotes';
import * as editorActions from 'store/actions/editors';
import { fetchEditorNoteState } from 'store/actions/editors';
import { FileLocationType } from 'store/api/files/config';
import { useGetNoteTranscriptionsQuery } from 'store/api/notes/hooks';
import { useGetPublicTemplatesQuery, useGetTemplatesQuery } from 'store/api/templates/hooks';
import {
	useDeleteTranscriptionMutation,
	useDeleteTranscriptionPartMutation,
	useRetryTranscriptionMutation,
	useUpdateTranscriptionPartMutation,
} from 'store/api/transcriptions/hooks';
import { selectAiScribePreferences } from 'store/slices/aiScribe/selectors';
import { useFeatureFlag } from 'store/slices/features/hooks';
import { addSnackbar } from 'store/slices/snackbars/slice';
import { AiScribePreferences } from 'types/aiScribe';
import { trackAiScribeEvent } from 'util/aiNotesTracking';
import { groupIntoBatches, splitFileIntoChunks } from 'util/hooks/useFileUpload';
import { useEffectEvent } from 'util/useEffectEvent';

import { RecordingStatus } from '../components/NoteSidePanel/PanelAiScribe/components/RecordButtonGroup';
import { AVAILABLE_LANGUAGES } from '../components/NoteSidePanel/PanelAiScribe/constants';
import { AvailableLanguage } from '../components/NoteSidePanel/PanelAiScribe/types';

import { useEditorStateContext } from './EditorStateContext';
import { useFloatingWidgetContext } from './FloatingWidgetContext';
import { useNoteSidePanelContext } from './NoteSidePanelContext';
import { useTiptapStateContext } from './TiptapStateContext';

export type ScribeType = 'LiveDictation' | 'LiveTranscription' | 'RecordedTranscription';

export const DEF_CONCURRENT_CHUNKS = 5;

const MAX_RECORDING_TIME_IN_SECONDS = 7200; // 2 hours

const TRANSCRIPTION_STUCK_THRESHOLD_IN_MINUTES = 12 * 60; // 12 hours

type TranscriptOptionVisibility = {
	timestamp: boolean;
	speaker: boolean;
};

type TranscriptionPartRequestState = 'idle' | 'loading' | 'success' | 'error';

export type AiScribeContextValue = {
	scribeType: ScribeType;
	availableAudioDevices: MediaDeviceInfo[];
	recordingStatus: RecordingStatus;
	permissionStatus: PermissionStatus;
	selectedLanguage: AvailableLanguage;
	selectedAudioInput: MediaDeviceInfo | null;
	transcripts: Transcript[];
	handleScribeTypeChange: (scribeType: ScribeType) => void;
	handleAudioInputChange: (audioInput: MediaDeviceInfo) => void;
	handleStartRecording: () => Promise<void>;
	handlePauseRecording: () => void;
	handleEndRecording: () => Promise<void>;
	handleStartUploading: (file: File, successCallback?: () => void, failCallback?: () => void) => void;
	handleLanguageChange: (lang: AvailableLanguage) => void;
	handleRequestMicrophonePermissions: () => void;
	reloadAudioInputDevices: () => Promise<void>;
	handleUpdateTranscriptionPart: (transcriptId: string, content: string, onSuccess?: () => void) => Promise<void>;
	handleRetryTranscription: () => Promise<void>;
	aiScribeState: AiScribeState;
	handleDeleteTranscription: () => Promise<void>;
	handleDeleteTranscriptionPart: () => Promise<void>;
	transcriptionPartToDelete: Transcript | null;
	handleSetTranscriptionPartToDelete: (transcript: Transcript) => void;
	resetTranscriptionPartToDelete: () => void;
	recordingTimeInSeconds: number;
	handleApplyAiTemplate: (template: ITemplate, applyTemplateOption: ApplyTemplateOption) => void;
	aiTemplates: {
		teamTemplates: ITemplate[];
		recommendedTemplates: ITemplate[];
	};
	isAiTemplatesLoading: boolean;
	isTranscriptionRecordingIdle: boolean;
	transcriptionPartRequestStatus: TranscriptionPartRequestState;
	resetTranscriptionIdleState: () => void;
	handleToggleTranscriptVisibility: (option: TranscriptOptionVisibility) => void;
	transcriptOptionVisibility: TranscriptOptionVisibility;
	audioStream: MediaStream | null;
	preferences?: AiScribePreferences;
	handleOpenSetting: () => void;
	handleCloseSetting: () => void;
	queueRef: React.MutableRefObject<PQueue>;
};

export const AiScribeContext = createContext<AiScribeContextValue | undefined>(undefined);

type AiScribeState = 'idle' | 'processing' | 'pending' | 'pending-upload' | 'finished' | 'setting' | 'failed';

type AiTranscriptionPartRecord = Record<string, 'uploading' | 'success' | 'failed'>;

const AiScribeContextProvider: React.FC<React.PropsWithChildren<unknown>> = ({ children }) => {
	const { formatMessage } = useIntl();

	const preferences = useSelector(selectAiScribePreferences);
	const {
		providerId,
		noteId,
		contactId,
		isUserProvider,
		editorId,
		position,
		currentTranscriptionId,
		setCurrentTranscriptionId,
	} = useEditorStateContext();
	const { setShowFloatingWidget } = useFloatingWidgetContext();
	const { setIsPanelExpanded } = useNoteSidePanelContext();
	const { handleSelectTemplate } = useNoteHandlersContext();
	const { forceRender } = useTiptapStateContext();
	const { hasFeatureFlagged: hasAgentProviderGoogleSpeechToText } = useFeatureFlag(
		'agent-provider-google-speech-to-text'
	);
	const { hasFeatureFlagged: hasDeferredTranscriptionCompletion } = useFeatureFlag(
		'deferred-transcription-completion'
	);
	const { hasFeatureFlagged: hasAiFeedbackSurvey } = useFeatureFlag('ai-feedback-survey');

	const [note, setNote] = useNoteStateContext();

	const queueRef = useRef(new PQueue({ concurrency: 1 }));

	const indexedDBTranscriptionsQueue = useRef(TranscriptionsQueue.getInstance());
	const hasUserEndedRecording = useRef<boolean>(false);

	const dispatch = useAppDispatch();

	const {
		data: transcriptsData,
		isSuccess: isTranscriptQuerySuccess,
		refetch: refetchTranscriptions,
	} = useGetNoteTranscriptionsQuery(
		{
			contactId: contactId ?? '',
			noteId: noteId ?? '',
		},
		{
			skip: !contactId || !noteId || !providerId,
		}
	);

	const [
		updateTranscriptionPart,
		{
			isLoading: isAiTranscriptPartUpdateLoading,
			isSuccess: isAiTranscriptPartUpdateRequestSuccess,
			isError: isAiTranscriptPartUpdateRequestError,
		},
	] = useUpdateTranscriptionPartMutation();
	const [
		deleteTranscriptionPart,
		{
			isLoading: isAiTranscriptPartDeleteLoading,
			isSuccess: isAiTranscriptPartDeleteRequestSuccess,
			isError: isAiTranscriptPartDeleteRequestError,
		},
	] = useDeleteTranscriptionPartMutation();
	const [deleteTranscription] = useDeleteTranscriptionMutation();
	const [retryTranscription] = useRetryTranscriptionMutation();
	const triggerCompleteParts = useCompleteTranscriptionParts();

	const { data: aiTemplates, isLoading: isTeamTemplatesLoading } = useGetTemplatesQuery(
		{
			query: {
				hasAiPrompts: true,
			},
		},
		{
			// do not call this in the client portal or intake
			skip: !isUserProvider,
		}
	);

	const { data: recommendedTemplates, isLoading: isRecommendedTemplatesLoading } = useGetPublicTemplatesQuery(
		{
			query: {
				hasAiPrompts: true,
				isRecommended: true,
			},
		},
		{
			skip: !isUserProvider,
		}
	);

	// recording configuration
	const [scribeType, setScribeType] = useState<ScribeType>('LiveTranscription');
	const [selectedLanguage, setSelectedLanguage] = useState<AvailableLanguage>(
		(preferences?.language && AVAILABLE_LANGUAGES.find((el) => el.code === preferences.language)) ||
			AVAILABLE_LANGUAGES[0]
	);
	const [selectedAudioInput, setSelectedAudioInput] = useState<MediaDeviceInfo | null>(null);

	const [transcripts, setTranscripts] = useState<Transcript[]>([]);

	const [transcriptionPartToDelete, setTranscriptionPartToDelete] = useState<Transcript | null>(null);

	const [availableAudioDevices, { refetch: refetchAvailableInputDevices }] = useAvailableInputAudioDevices({
		onAvailableDevicesChange: (devices) => {
			const matchedDevice =
				selectedAudioInput?.deviceId &&
				devices.find((device) => device.deviceId === selectedAudioInput.deviceId);

			/**
			 * If the current selected device is available, do nothing
			 * If the selected device is available but a default, then the previous device must also be a default,
			 * so we should change the default device to the current device
			 */
			const isCurrentDeviceAvailable = matchedDevice && selectedAudioInput.deviceId !== 'default';
			if (isCurrentDeviceAvailable) return;

			const defaultDevice = devices?.[0];
			handleAudioInputChange(defaultDevice);
		},
	});

	const { status: permissionStatus, requestMicrophonePermissions } = useInputAudioPermissions();

	// ai scribe screen states
	const [aiScribeState, setAiScribeState] = useState<AiScribeState>('idle');
	const [recordingTimeInSeconds, setRecordingTimeInSeconds] = useState<number>(0);
	const [lastKnownRecordingTime, setLastKnownRecordingTime] = useState<number>(0);
	const transcriptionPartsStatus = useRef<AiTranscriptionPartRecord>({});

	// transcripts state
	const [transcriptOptionVisibility, setTranscriptOptionVisibility] = useState<TranscriptOptionVisibility>({
		timestamp: true,
		speaker: false,
	});

	const transcriptionJob = useRef<{
		transcriptionId: string | null;
		currentPartNumber: number;
	}>({
		transcriptionId: null,
		currentPartNumber: 0,
	});

	/**
	 * Automatic recovery for stuck transcriptions
	 * Checks if transcription is stuck and queue is idle, then forces completion
	 */
	const checkAndRecoverStuckTranscription = useCallback(
		async (transcription: any) => {
			if (!transcription || !transcription.id) return;

			// Only attempt recovery for InProgress or PendingCompletion status
			if (transcription.status !== 'InProgress' && transcription.status !== 'PendingCompletion') {
				return;
			}

			// Check if transcription has been stuck for a reasonable time (30 minutes)
			// This gives the backend plenty of time to process longer transcriptions
			const createdTime = new Date(transcription.createdDateTimeUtc).getTime();
			const currentTime = Date.now();
			const minutesStuck = (currentTime - createdTime) / (1000 * 60);

			if (minutesStuck < TRANSCRIPTION_STUCK_THRESHOLD_IN_MINUTES) {
				// Not stuck long enough, don't intervene
				return;
			}

			// Check if queue is idle (no active items being processed)
			const isQueueIdle = queueRef.current.size === 0 && queueRef.current.pending === 0;

			if (!isQueueIdle) {
				// Queue is still active, let normal processing continue
				return;
			}

			// Check if there are any items in IndexedDB queue for this transcription
			try {
				const allQueueItems = await indexedDBTranscriptionsQueue.current.getAllItems();
				const transcriptionQueueItems = allQueueItems.filter(
					(item) => item.value.transcriptionId === transcription.id
				);

				// There are still items in the queue for this transcription
				if (transcriptionQueueItems.length > 0) {
					return;
				}
			} catch (error) {
				logger.error('Error checking IndexedDB queue for stuck transcription', {
					transcriptionId: transcription.id,
					error,
				});
				return;
			}

			try {
				// Transcription is stuck and no active processing - attempt recovery
				logger.warn('Detected stuck transcription, attempting automatic recovery', {
					transcriptionId: transcription.id,
					status: transcription.status,
					minutesStuck: Math.round(minutesStuck),
					createdTime: transcription.createdDateTimeUtc,
				});

				await triggerCompleteParts({
					transcriptionId: transcription.id,
					contactId: transcription.contactId,
					noteId: transcription.noteId,
				});
			} catch (error) {
				logger.error('Failed to recover stuck transcription', {
					transcriptionId: transcription.id,
					status: transcription.status,
					minutesStuck: Math.round(minutesStuck),
					error,
				});
			}
		},
		[triggerCompleteParts]
	);

	useEffect(() => {
		const handlePermissionsChange = async () => {
			const availableDevices = await refetchAvailableInputDevices();
			if (availableDevices.length) {
				setSelectedAudioInput(availableDevices[0]);
			}
		};

		if (permissionStatus === 'granted') {
			handlePermissionsChange();
		}

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [permissionStatus]);

	useEffect(() => {
		if (!transcriptionJob.current.transcriptionId && !currentTranscriptionId) {
			return;
		}

		if (transcriptionJob.current.transcriptionId !== currentTranscriptionId) {
			setCurrentTranscriptionId(transcriptionJob.current.transcriptionId);
		}
	}, [transcriptionJob.current.transcriptionId, currentTranscriptionId, setCurrentTranscriptionId]);

	useEffect(() => {
		if (isTranscriptQuerySuccess && transcriptsData) {
			if (transcriptsData.length > 0) {
				const transcriptions = transcriptsData;
				const lastTranscription = transcriptions[transcriptions.length - 1];

				const updateTranscripts = (status: AiScribeState) => {
					setAiScribeState((oldState) => {
						// Need to check the old state to avoid showing success message on the first load
						// idle => finished means the transcriptions are already done during the first load
						if (oldState !== 'idle' && lastTranscription.status === 'Completed' && status === 'finished') {
							dispatch(
								addSnackbar({
									messageId: langIds.TranscriptSuccessSnackbar,
									variant: 'success',
									id: langIds.TranscriptSuccessSnackbar,
								})
							);
							dispatch(
								fetchEditorNoteState({
									editorId,
									forceRefetch: true,
									onSuccess(note) {
										setNote(note);
										forceRender();
									},
								})
							);
						}

						return status;
					});

					if (status === 'processing') {
						const lastTranscript = lastTranscription.transcripts.at(-1);
						if (!lastTranscript) return;

						const existingTimeInSeconds = lastTranscript.endTime
							.split(':')
							.reduce((acc, time) => 60 * acc + +time, 0);

						setRecordingTimeInSeconds((prev) => prev || existingTimeInSeconds);
						setLastKnownRecordingTime((prev) => prev || existingTimeInSeconds);
					}

					setTranscripts(
						lastTranscription.transcripts.map((i) => ({
							...i,
						}))
					);
				};

				// we are not uploading parts or calling the API to complete the transcription
				const isQueueIdle = queueRef.current.size === 0 && queueRef.current.pending === 0;

				if (lastTranscription.status === 'InProgress' && isQueueIdle) {
					updateTranscripts('processing');

					// Check for stuck transcription and attempt automatic recovery
					checkAndRecoverStuckTranscription(lastTranscription);
				}

				if (lastTranscription.status === 'PendingCompletion') {
					updateTranscripts('pending');

					trackAiScribeEvent('Transcription pending completion', {
						scribeType,
						transcriptionId: lastTranscription.id,
					});

					// Check for stuck transcription and attempt automatic recovery
					checkAndRecoverStuckTranscription(lastTranscription);
				}

				if (lastTranscription.status === 'Completed') {
					updateTranscripts('finished');

					trackAiScribeEvent('Transcription completed', {
						scribeType,
						transcriptionId: lastTranscription.id,
					});

					if (hasAiFeedbackSurvey) {
						// collapse the panel if transcription is finished when editor is in bottom right position
						if (position === 'BottomRight') {
							setIsPanelExpanded(false);
						}
						// show feedback widget after transcription is finished
						setShowFloatingWidget(true);
					}
				}

				if (lastTranscription.status === 'Failed') {
					updateTranscripts('failed');
				}

				const lastTranscriptPartNumber =
					lastTranscription.transcripts.length > 0
						? lastTranscription.transcripts[lastTranscription.transcripts.length - 1].partNumber
						: lastTranscription.transcripts.length;

				transcriptionJob.current = {
					transcriptionId: lastTranscription.id,
					currentPartNumber: lastTranscriptPartNumber,
				};
			}
		}
		/** @note -- exclude `position` from the dependencies because it triggers the effect when resizing the editor */
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [
		checkAndRecoverStuckTranscription,
		dispatch,
		editorId,
		forceRender,
		hasAiFeedbackSurvey,
		isTranscriptQuerySuccess,
		setIsPanelExpanded,
		setNote,
		setShowFloatingWidget,
		transcriptsData,
	]);

	const handleLiveTranscriptionPartUpload = useCallback(
		async (blob: Blob, startTime?: string, endTime?: string, maxRetries: number = 3) => {
			const { transcriptionId, currentPartNumber } = transcriptionJob.current;

			if (!transcriptionId) {
				throw new Error('Transcription ID is required to upload audio.');
			}

			let attempts = 0;
			let success = false;

			while (attempts < maxRetries && !success) {
				try {
					attempts++;

					transcriptionPartsStatus.current[currentPartNumber] = 'uploading';

					const newTranscripts = await api.uploadTranscriptionPart({
						providerId,
						transcriptionId,
						partNumber: currentPartNumber,
						startTime: startTime || '00:00:00',
						endTime: endTime,
						dto: blob,
						context: { useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText },
					});
					transcriptionPartsStatus.current[currentPartNumber] = 'success';
					transcriptionJob.current.currentPartNumber += 1;
					setTranscripts((prevTranscripts) => [...prevTranscripts, ...newTranscripts]);
					success = true;
				} catch (error) {
					if (attempts >= maxRetries) {
						transcriptionPartsStatus.current[currentPartNumber] = 'failed';
						logger.error(error, {
							message: 'Unable to upload transcription audio chunk',
							partNumber: currentPartNumber,
						});
					}
				}
			}
		},
		[providerId, hasAgentProviderGoogleSpeechToText]
	);

	const addAudioBlobToIndexedDB = async ({
		startTime,
		endTime,
		blob,
		isLastPart,
	}: {
		startTime?: string;
		endTime?: string;
		blob?: Blob;
		isLastPart?: boolean;
	}) => {
		await indexedDBTranscriptionsQueue.current.enqueue({
			audioChunk: blob,
			noteId: noteId as string,
			contactId: contactId as string,
			transcriptionId: transcriptionJob.current.transcriptionId as string,
			metadata: {
				partNumber: transcriptionJob.current.currentPartNumber,
				startTime: startTime,
				endTime: endTime,
				isLastPart: !!isLastPart,
			},
		});

		transcriptionJob.current.currentPartNumber += 1;
	};

	const {
		startRecording,
		stopRecording,
		pauseRecording,
		resumeRecording,
		status: recordingStatus,
		audioStream,
		silentChunkCount,
		resetSilentChunkCount,
	} = useAudioInputRecorder({
		options: {
			offsetInSeconds: lastKnownRecordingTime,
			chunkOnSilence: scribeType !== 'LiveDictation',
		},
		onTick: (elapsedTimeInSeconds: number) => {
			setRecordingTimeInSeconds(elapsedTimeInSeconds);
			handleOvertimeRecording(elapsedTimeInSeconds);
		},
		onStream: async (blob, startTime, endTime) => {
			if (hasDeferredTranscriptionCompletion) {
				await addAudioBlobToIndexedDB({
					startTime: startTime || '00:00:00',
					endTime: endTime || '00:00:00',
					blob,
				});
			} else {
				queueRef.current.add(() => handleLiveTranscriptionPartUpload(blob, startTime, endTime));
			}
		},
	});

	const displayedWarningsRef = useRef<Set<number>>(new Set());

	const handleRequestMicrophonePermissions = useEffectEvent(async () => {
		await requestMicrophonePermissions();
	});

	const resetTranscriptionPartToDelete = useCallback(() => {
		setTranscriptionPartToDelete(null);
	}, []);

	const handleSetTranscriptionPartToDelete = useEffectEvent((transcript: Transcript | null) => {
		setTranscriptionPartToDelete(transcript);
	});

	const handleRetryTranscription = useEffectEvent(async () => {
		if (!transcriptionJob.current.transcriptionId) {
			throw new Error('Transcription ID is required to retry transcription.');
		}

		try {
			await retryTranscription({ transcriptionId: transcriptionJob.current.transcriptionId });
			setTranscripts([]);
			setAiScribeState('processing');
			refetchTranscriptions();
		} catch (error) {
			dispatch(addErrorSnackbar(error));
		}
	});

	const handleUpdateTranscriptionPart = useEffectEvent(
		async (transcriptId: string, content: string, onSuccess?: () => void) => {
			if (!transcriptionJob?.current?.transcriptionId || !transcriptId || !content) {
				throw new Error(
					'Transcription ID, Transcript ID, and content are required to update transcription part.'
				);
			}

			try {
				await updateTranscriptionPart({
					transcriptionId: transcriptionJob.current.transcriptionId,
					transcriptId,
					content,
				});

				setTranscripts((prevTranscripts) =>
					prevTranscripts.map((transcript) => {
						if (transcript.id === transcriptId) {
							return {
								...transcript,
								content,
							};
						}
						return transcript;
					})
				);
				trackAiScribeEvent('User updated transcription part');
				dispatch(
					addSnackbar({
						id: 'transcription-part-update',
						variant: 'success',
						messageId: langIds.SuccessfullyUpdatedTranscriptionPart,
					})
				);
				onSuccess?.();
			} catch (error) {
				logger.error(error, { message: 'Unable to update transcription part' });
				dispatch(addErrorSnackbar(error));
			}
		}
	);

	const handleDeleteTranscriptionPart = useCallback(async () => {
		const transcriptId = transcriptionPartToDelete?.id;

		if (!transcriptionJob?.current?.transcriptionId || !transcriptId) {
			throw new Error('Transcription ID or Transcript ID are required to delete transcription part.');
		}

		try {
			await deleteTranscriptionPart({
				transcriptionId: transcriptionJob.current.transcriptionId,
				transcriptId,
			});

			setTranscripts((prevTranscripts) => prevTranscripts.filter((transcript) => transcript.id !== transcriptId));
			trackAiScribeEvent('User deleted transcription part');
			dispatch(
				addSnackbar({
					id: 'transcription-part-delete',
					variant: 'success',
					messageId: langIds.SuccessfullyDeletedTranscriptionPart,
				})
			);
			resetTranscriptionPartToDelete();
		} catch (error) {
			logger.error(error, { message: 'Unable to delete transcription part' });
			dispatch(addErrorSnackbar(error));
		}
	}, [
		dispatch,
		transcriptionPartToDelete,
		resetTranscriptionPartToDelete,
		deleteTranscriptionPart,
		transcriptionJob,
	]);

	const handleDeleteTranscription = useEffectEvent(async () => {
		if (!transcriptionJob?.current?.transcriptionId || !noteId || !contactId) {
			throw new Error('Transcription ID, note ID, and contact ID are required to delete transcription.');
		}

		try {
			await deleteTranscription({
				noteId: noteId || '',
				contactId: contactId || '',
				transcriptionId: transcriptionJob.current.transcriptionId,
			});

			// Mark transcription as completed since it's deleted
			TranscriptionStateManager.markTranscriptionCompleted(providerId, transcriptionJob.current.transcriptionId);

			transcriptionJob.current = {
				currentPartNumber: 0,
				transcriptionId: null,
			};
			transcriptionPartsStatus.current = {};
			setTranscripts([]);
			setRecordingTimeInSeconds(0);
			setLastKnownRecordingTime(0);
			setAiScribeState('idle');
			setScribeType('LiveTranscription');
			trackAiScribeEvent('User deleted transcription');
		} catch (error) {
			logger.error(error, { message: 'Unable to delete transcription' });
			dispatch(addErrorSnackbar(error));
		}
	});

	const handleAudioInputChange = useEffectEvent(async (audioInput: MediaDeviceInfo) => {
		// if it's processing or recording, start recording with the new deviceId
		if (aiScribeState === 'processing' || recordingStatus === 'recording') {
			await startRecording({ deviceId: audioInput.deviceId });
		}

		setSelectedAudioInput(audioInput);
		trackAiScribeEvent('User changed audio input device');
	});

	const reloadAudioInputDevices = useEffectEvent(async () => {
		const devices = await refetchAvailableInputDevices();
		setSelectedAudioInput(devices?.[0]);
	});

	const handleStartRecording = useEffectEvent(async () => {
		if (!noteId) {
			throw new Error('Note ID is required to start recording.');
		}

		trackAiScribeEvent('User started transcribing live audio', {
			type: scribeType,
		});

		if (permissionStatus !== 'granted') {
			try {
				await requestMicrophonePermissions();
				const devices = await refetchAvailableInputDevices();
				setSelectedAudioInput(devices?.[0]);
			} catch (error) {
				throw new Error('Microphone permissions are required to start recording.');
			}
		}

		if (recordingStatus === 'idle') {
			// allow users to continue the transcriptions, otherwise, if it's new
			// create a new one in the backend
			if (!transcriptionJob.current.transcriptionId) {
				const { transcriptionId } = await api.initializeTranscription({
					providerId,
					dto: {
						type: scribeType,
						noteId,
						language: selectedLanguage.code,
					},
				});
				transcriptionJob.current.transcriptionId = transcriptionId;

				// Mark transcription as active so it can be resumed if tab closes
				TranscriptionStateManager.markTranscriptionActive(providerId, transcriptionId);
			}

			await startRecording({ deviceId: selectedAudioInput?.deviceId || 'default' });
			setAiScribeState('processing');
		}

		if (recordingStatus === 'paused') {
			resumeRecording();
			trackAiScribeEvent('User resumed transcribing live audio', {
				scribeType,
			});
		}
	});

	const handlePauseRecording = useEffectEvent(() => {
		pauseRecording();
		trackAiScribeEvent('User paused transcribing live audio', {
			scribeType,
		});
	});

	const handleEndRecording = useEffectEvent(async () => {
		if (!transcriptionJob.current.transcriptionId) {
			throw new Error('Transcription job does not exist.');
		}

		try {
			hasUserEndedRecording.current = true;
			await stopRecording();

			// For deferred transcription; have transition state to prevent UX glitch
			setAiScribeState(hasDeferredTranscriptionCompletion ? 'pending-upload' : 'pending');

			if (hasDeferredTranscriptionCompletion) {
				await addAudioBlobToIndexedDB({
					isLastPart: true,
				});
			} else {
				// wait for all the parts to complete uploading before calling `/complete`
				await queueRef.current.onIdle();
			}

			await api.completeTranscription({
				providerId,
				transcriptionId: transcriptionJob.current.transcriptionId,
				context: {
					useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText,
					deferredTranscriptionCompletion: hasDeferredTranscriptionCompletion,
				},
			});

			// Mark transcription as completed (no longer needs resumption)
			TranscriptionStateManager.markTranscriptionCompleted(providerId, transcriptionJob.current.transcriptionId);

			transcriptionJob.current = {
				transcriptionId: null,
				currentPartNumber: 0,
			};
			trackAiScribeEvent('User ended transcribing live audio', {
				scribeType: scribeType,
			});

			// For deferred transcription; we only gonna refetch after fully uploaded parts
			if (!hasDeferredTranscriptionCompletion) {
				refetchTranscriptions();
			}
		} catch (error) {
			logger.error(error);
		}
	});

	const handleOvertimeRecording = useEffectEvent((timeInSeconds: number) => {
		const FINAL_WARNING_TIME_IN_SECONDS = MAX_RECORDING_TIME_IN_SECONDS - 30; // 30 seconds before the recording ends

		const warningMap = new Map([
			[MAX_RECORDING_TIME_IN_SECONDS - 60 * 30, { timeValue: 30, unit: 'minutes' }],
			[MAX_RECORDING_TIME_IN_SECONDS - 60 * 15, { timeValue: 15, unit: 'minutes' }],
			[MAX_RECORDING_TIME_IN_SECONDS - 60 * 5, { timeValue: 5, unit: 'minutes' }],
			[MAX_RECORDING_TIME_IN_SECONDS - 60, { timeValue: 1, unit: 'minute' }],
			[FINAL_WARNING_TIME_IN_SECONDS, { timeValue: 30, unit: 'seconds' }],
		]);

		if (warningMap.has(timeInSeconds) && !displayedWarningsRef.current.has(timeInSeconds)) {
			dispatch(
				addSnackbar({
					id: uuid(),
					messageId: langIds.TranscriptionOvertimeWarning,
					messageValues: {
						scribeType:
							scribeType === 'LiveTranscription'
								? formatMessage({ id: langIds.Transcription })
								: formatMessage({ id: langIds.Dictation }),
						...warningMap.get(timeInSeconds),
						strong: (chunk: string) => <strong>{chunk}</strong>,
					},
					variant: 'warning',
				})
			);
		}

		if (FINAL_WARNING_TIME_IN_SECONDS <= timeInSeconds && recordingStatus === 'recording') {
			handleEndRecording();
		}
	});

	const handleStartUploading = useEffectEvent(
		async (file: File, successCallback?: () => void, failCallback?: () => void) => {
			if (!noteId) {
				throw new Error('Note ID is required to start recording.');
			}

			trackAiScribeEvent('User started transcribing audio file', {
				scribeType: 'RecordedTranscription',
			});

			const initializeFileUploadResult = await api.initializeTranscription({
				providerId,
				dto: { type: scribeType, noteId, language: selectedLanguage.code },
			});

			if ('error' in initializeFileUploadResult) throw new Error();

			// Mark transcription as active so it can be resumed if tab closes
			TranscriptionStateManager.markTranscriptionActive(providerId, initializeFileUploadResult.transcriptionId);

			const { transcriptionId, uploadId, fileId } = initializeFileUploadResult;

			transcriptionJob.current.transcriptionId = transcriptionId;

			const UPLOAD_CHUNK_SIZE = 1024 * 1024 * 5; // 5 mb

			const chunks = splitFileIntoChunks(file, UPLOAD_CHUNK_SIZE);
			const chunkGroups = groupIntoBatches(chunks, DEF_CONCURRENT_CHUNKS);
			const uploadFileChunks: PromiseSettledResult<Transcript[]>[] = [];

			for (const chunkGroup of chunkGroups) {
				const results = await Promise.allSettled(
					chunkGroup.map(({ chunk, sequenceNumber }) => {
						return api.uploadTranscriptionPart({
							providerId,
							transcriptionId,
							uploadId,
							fileId,
							partNumber: sequenceNumber,
							dto: new Blob([chunk]),
							context: { useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText },
						});
					})
				);

				results.forEach((result) => {
					uploadFileChunks.push(result);
				});
			}

			if (uploadFileChunks.some((result) => result.status === 'rejected')) {
				if (failCallback) {
					failCallback();
				}
			} else {
				try {
					await api.completeTranscription({
						providerId,
						transcriptionId: transcriptionJob.current.transcriptionId,
						requestBody: {
							fileId,
							uploadId,
							fileLocationType: FileLocationType.Transcription,
							contentType: file.type,
						},
						context: { useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText },
					});

					transcriptionJob.current = {
						transcriptionId: null,
						currentPartNumber: 0,
					};

					if (successCallback) {
						successCallback();
					}

					refetchTranscriptions();
				} catch (error) {
					// TODO: handle error
					console.error(error);
				}
			}
		}
	);

	const handleLanguageChange = useEffectEvent((lang: AvailableLanguage) => {
		setSelectedLanguage(lang);
		trackAiScribeEvent('User changed language', {
			languageCode: lang.code,
		});
	});

	const handleScribeTypeChange = useEffectEvent((type: ScribeType) => {
		if (type) {
			setScribeType(type);
		}
	});

	const handleApplyAiTemplate = useEffectEvent(
		async (template: ITemplate, applyTemplateOption: ApplyTemplateOption) => {
			if (!transcriptionJob.current.transcriptionId || !contactId) {
				return;
			}

			trackAiScribeEvent('User applied AI template', {
				applyTemplateOption,
				templateId: template.id,
			});

			// branch out logic here, manually call the API to generate the note
			// instead of relying on the Common.tsx. The backend generates the note
			// and we don't need to update the current Editor instance. Apply template
			// from the Templates list creates an Editor instance first, then waits
			// for the backend to apply the template. This doesn't display the generating load animation inside the note
			// but rather shows the loading inside the confirmation modal
			if (applyTemplateOption === 'CreateNew') {
				try {
					// Generate a new editor ID
					const newEditorId = uuid();
					const modalPosition = position === 'FullScreen' ? 'FullScreen' : 'BottomRight';

					// This batch dispatch is used to create a generating skeleton editor instance
					batch(() => {
						// Collapse the current editor instance if it's in full screen
						if (position === 'FullScreen') {
							dispatch(
								updateBottomModal({
									modalId: editorId,
									position: POSITION.BottomRight,
								})
							);
						}

						// Create a new bottom modal instance
						dispatch(
							createBottomModal({
								modalId: newEditorId,
								type: 'Note',
								position: modalPosition,
							})
						);

						// Create a new editor instance
						dispatch(
							editorActions.createEditor({
								editorId: newEditorId,
								type: 'Note',
								context: {
									contactId,
									selectedView: 'View',
									isApplyingTranscript: true,
								},
							})
						);
					});

					// Call the createNoteFromTemplate API
					const newNote = await api.createNoteFromTemplate(providerId, contactId, {
						transcriptionId: transcriptionJob.current.transcriptionId,
						...(template.isPublic ? { publicTemplateId: template.id } : { templateId: template.id }),
					});

					batch(() => {
						dispatch(contactNoteFetched(newNote));

						// Close the existing editor/modal when the API is finished.
						dispatch(editorActions.deleteEditor(newEditorId));
						dispatch(removeBottomModal(newEditorId));

						dispatch(
							editorActions.renderEditorModal({
								type: 'Note',
								context: {
									...newNote,
									noteId: newNote.id,
									selectedView: hasForms(newNote.form) ? 'Responder' : 'Edit',
									contactId,
								},
								position: modalPosition,
							})
						);

						dispatch(
							addSnackbar({
								id: uuid(),
								messageId: langIds.NewNoteCreated,
								variant: 'success',
							})
						);
					});
				} catch (e) {
					dispatch(addErrorSnackbar(e));
					logger.error(e, {
						message: 'Unable to create a new note using an ai-template',
						templateId: template.id,
					});
				}
			} else {
				handleSelectTemplate({
					id: template.id,
					title: note.title || template.title || '',
					transcriptionId: transcriptionJob.current.transcriptionId,
					content: template.content,
					contentJson: template.contentJson,
					form: template.form,
					isPublic: template.isPublic,
					applyTemplateOption: applyTemplateOption,
				});
			}
		}
	);

	const handleToggleTranscriptVisibility = useCallback((option: TranscriptOptionVisibility) => {
		if (option) {
			setTranscriptOptionVisibility(option);
		}
		trackAiScribeEvent('User toggled transcript elements visibility', {
			timestamp: option.timestamp,
			speaker: option.speaker,
		});
	}, []);

	const handleOpenSetting = useEffectEvent(() => {
		setAiScribeState('setting');
		trackAiScribeEvent('User opened transcription preferences');
	});

	const handleCloseSetting = useEffectEvent(() => {
		// if the user closes the setting, and there's no transcriptionId, start recording
		// if there's a transcriptionId, it means the user has already on the transcription screen
		if (transcriptionJob.current.transcriptionId) {
			setAiScribeState('finished');
			trackAiScribeEvent('User closed transcription preferences');
			return;
		}
		return handleStartRecording();
	});

	const resetTranscriptionIdleState = useEffectEvent(() => {
		resetSilentChunkCount();
	});

	const transcriptionPartRequestStatus = useMemo(() => {
		if (isAiTranscriptPartUpdateLoading || isAiTranscriptPartDeleteLoading) return 'loading';
		if (isAiTranscriptPartUpdateRequestSuccess || isAiTranscriptPartDeleteRequestSuccess) return 'success';
		if (isAiTranscriptPartUpdateRequestError || isAiTranscriptPartDeleteRequestError) return 'error';
		return 'idle';
	}, [
		isAiTranscriptPartUpdateLoading,
		isAiTranscriptPartDeleteLoading,
		isAiTranscriptPartDeleteRequestSuccess,
		isAiTranscriptPartUpdateRequestSuccess,
		isAiTranscriptPartDeleteRequestError,
		isAiTranscriptPartUpdateRequestError,
	]);

	return (
		<AiScribeContext.Provider
			value={{
				scribeType,
				availableAudioDevices,
				recordingStatus,
				selectedLanguage,
				selectedAudioInput,
				permissionStatus,
				transcripts,
				handleScribeTypeChange,
				handleAudioInputChange,
				handleStartRecording,
				handlePauseRecording,
				handleEndRecording,
				handleLanguageChange,
				handleRequestMicrophonePermissions,
				handleRetryTranscription,
				reloadAudioInputDevices,
				aiScribeState,
				handleUpdateTranscriptionPart,
				handleDeleteTranscription,
				handleDeleteTranscriptionPart,
				recordingTimeInSeconds,
				handleApplyAiTemplate,
				aiTemplates: {
					recommendedTemplates: recommendedTemplates?.items || [],
					teamTemplates: aiTemplates?.items || [],
				},
				transcriptionPartRequestStatus,
				isAiTemplatesLoading: isTeamTemplatesLoading || isRecommendedTemplatesLoading,
				handleStartUploading,
				handleToggleTranscriptVisibility,
				transcriptOptionVisibility,
				audioStream,
				preferences,
				handleOpenSetting,
				handleCloseSetting,
				queueRef,
				isTranscriptionRecordingIdle: silentChunkCount >= SILENCE_CHUNK_COUNT_PER_MINUTE,
				resetTranscriptionIdleState,
				transcriptionPartToDelete,
				handleSetTranscriptionPartToDelete,
				resetTranscriptionPartToDelete,
			}}
		>
			{children}
		</AiScribeContext.Provider>
	);
};

const useAiScribeContext = () => {
	const context = useContext(AiScribeContext);

	if (!context) {
		throw new Error('useAiScribeContext must be used within a AiScribeContextProvider');
	}

	return context;
};

export { AiScribeContextProvider, useAiScribeContext };
