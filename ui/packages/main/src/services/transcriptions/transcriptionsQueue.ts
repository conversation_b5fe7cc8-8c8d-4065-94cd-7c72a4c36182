import { logger } from '@carepatron/utilities';

import { getTabSessionId } from './sessionUtils';

export type TranscriptionPartQueueMessage = {
	audioChunk?: Blob;
	transcriptionId: string;
	noteId: string;
	contactId: string;
	sessionId: string; // Identifies which tab/session owns this transcription part
	metadata: {
		partNumber: number;
		startTime?: string;
		endTime?: string;
		isLastPart: boolean;
	};
};

const DB_NAME = 'transcriptions-queue';
const DB_VERSION = 1;
const LOCK_TIMEOUT = 30000; // 30 seconds

class TranscriptionsQueue {
	private static instances: Map<string, TranscriptionsQueue> = new Map();
	private db: IDBDatabase | null = null;
	private locked: boolean;
	private lockTimestamp: number | null = null;
	private sessionId: string;

	private constructor(sessionId: string) {
		this.sessionId = sessionId;
		this._initDB();
		this.locked = false;
		this.lockTimestamp = null;
	}

	/**
	 * Get a queue instance for the current tab/session
	 * Each tab gets its own queue instance with a unique session ID
	 */
	public static getInstance(): TranscriptionsQueue {
		// Use the shared session utility
		const sessionId = getTabSessionId();

		// Return existing instance or create new one
		if (!this.instances.has(sessionId)) {
			this.instances.set(sessionId, new TranscriptionsQueue(sessionId));
		}

		return this.instances.get(sessionId)!;
	}

	/**
	 * Get the session ID for this queue instance
	 */
	public getSessionId(): string {
		return this.sessionId;
	}

	/**
	 * Enqueue a transcription part - sessionId is automatically added
	 */
	public enqueue(item: Omit<TranscriptionPartQueueMessage, 'sessionId'>): Promise<IDBValidKey> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				return null;
			}

			// Automatically add sessionId to the item
			const itemWithSession: TranscriptionPartQueueMessage = {
				...item,
				sessionId: this.sessionId,
			};

			const transaction = this.db.transaction(['queue'], 'readwrite');
			const store = transaction.objectStore('queue');
			const request = store.add(itemWithSession);

			request.onsuccess = () => resolve(request.result);
			request.onerror = (event: Event) => reject((event.target as IDBRequest).error);
		});
	}

	public unlockFront() {
		this.locked = false;
	}

	public peekAndLockFront(): Promise<{
		key: IDBValidKey;
		value: TranscriptionPartQueueMessage;
	} | null> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				resolve(null);
				return;
			}

			// Check if lock has expired
			const now = Date.now();
			const isLockExpired = !this.lockTimestamp || now - this.lockTimestamp > LOCK_TIMEOUT;

			if (this.locked && !isLockExpired) {
				resolve(null);
				return;
			}

			this.locked = true;
			this.lockTimestamp = now;
			const transaction = this.db.transaction(['queue'], 'readonly');
			const store = transaction.objectStore('queue');
			const request = store.openCursor();

			request.onsuccess = (event: Event) => {
				const cursor = (event.target as IDBRequest).result;
				if (cursor) {
					resolve({ key: cursor.primaryKey, value: cursor.value });
				} else {
					this.locked = false;
					this.lockTimestamp = null;
					resolve(null);
				}
			};

			request.onerror = (event: Event) => {
				this.locked = false;
				this.lockTimestamp = null;
				reject((event.target as IDBRequest).error);
			};
		});
	}

	public clear(): Promise<void> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				return null;
			}
			const transaction = this.db.transaction(['queue'], 'readwrite');
			const store = transaction.objectStore('queue');
			const request = store.clear();
			request.onsuccess = () => {
				this.locked = false;
				this.lockTimestamp = null;
				resolve();
			};
			request.onerror = (event: Event) => reject((event.target as IDBRequest).error);
		});
	}

	public deleteByKey(key: IDBValidKey): Promise<void> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				return null;
			}
			const transaction = this.db.transaction(['queue'], 'readwrite');
			const store = transaction.objectStore('queue');
			const request = store.delete(key);
			request.onsuccess = () => {
				this.locked = false;
				this.lockTimestamp = null;
				resolve();
			};
			request.onerror = (event: Event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Update an existing item in the queue by key
	 */
	public updateItem(key: IDBValidKey, item: TranscriptionPartQueueMessage): Promise<void> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				return null;
			}
			const transaction = this.db.transaction(['queue'], 'readwrite');
			const store = transaction.objectStore('queue');
			const request = store.put(item, key);
			request.onsuccess = () => resolve();
			request.onerror = (event: Event) => reject((event.target as IDBRequest).error);
		});
	}

	public getAllItems(): Promise<{ key: IDBValidKey; value: TranscriptionPartQueueMessage }[]> {
		return new Promise((resolve, reject) => {
			if (!this.db) {
				resolve([]);
				return;
			}
			const transaction = this.db.transaction(['queue'], 'readonly');
			const store = transaction.objectStore('queue');
			const request = store.openCursor();
			const items: { key: IDBValidKey; value: TranscriptionPartQueueMessage }[] = [];

			request.onsuccess = (event: Event) => {
				const cursor = (event.target as IDBRequest).result;
				if (cursor) {
					items.push({ key: cursor.primaryKey, value: cursor.value });
					cursor.continue();
				} else {
					resolve(items);
				}
			};

			request.onerror = (event: Event) => reject((event.target as IDBRequest).error);
		});
	}

	private _initDB(): void {
		const request: IDBOpenDBRequest = indexedDB.open(DB_NAME, DB_VERSION);

		request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
			const db = (event.target as IDBOpenDBRequest).result;
			if (!db.objectStoreNames.contains('queue')) {
				db.createObjectStore('queue', { autoIncrement: true });
			}
		};

		request.onsuccess = (event: Event) => {
			this.db = (event.target as IDBOpenDBRequest).result;
		};

		request.onerror = () => {
			logger.error('IndexedDB initialization error');
		};
	}
}

export default TranscriptionsQueue;
