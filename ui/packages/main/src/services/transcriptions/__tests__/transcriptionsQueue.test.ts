import { TranscriptionPartQueueMessage } from '../transcriptionsQueue';

// Mock logger
jest.mock('@carepatron/utilities', () => ({
	logger: {
		error: jest.fn(),
		info: jest.fn(),
		warn: jest.fn(),
	},
}));

// Create a mock queue class that implements the methods we want to test
class MockTranscriptionsQueue {
	private locked = false;
	private lockTimestamp: number | null = null;
	private db: any = {};
	private static instances = new Map<string, MockTranscriptionsQueue>();

	constructor(private sessionId: string) {}

	static getInstance(sessionId: string): MockTranscriptionsQueue {
		if (!this.instances.has(sessionId)) {
			this.instances.set(sessionId, new MockTranscriptionsQueue(sessionId));
		}
		return this.instances.get(sessionId)!;
	}

	static clearInstances() {
		this.instances.clear();
	}

	async updateItem(key: string, value: TranscriptionPartQueueMessage): Promise<void | null> {
		if (!this.db) return null;
		// Simulate successful update
		return Promise.resolve();
	}

	async peekAndLockFront(): Promise<{ key: string; value: TranscriptionPartQueueMessage } | null> {
		const LOCK_TIMEOUT = 30 * 1000; // 30 seconds
		const currentTime = Date.now();

		// Check if lock has expired
		if (this.locked && this.lockTimestamp && currentTime - this.lockTimestamp > LOCK_TIMEOUT) {
			this.locked = false;
			this.lockTimestamp = null;
		}

		// If already locked and not expired, return null
		if (this.locked) {
			return null;
		}

		// Acquire lock
		this.locked = true;
		this.lockTimestamp = currentTime;

		// Return null for empty queue (for testing)
		return null;
	}

	async deleteByKey(key: string): Promise<void> {
		// Clear lock when deleting
		this.locked = false;
		this.lockTimestamp = null;
		return Promise.resolve();
	}

	async clear(): Promise<void> {
		// Clear lock when clearing queue
		this.locked = false;
		this.lockTimestamp = null;
		return Promise.resolve();
	}

	unlockFront(): void {
		this.locked = false;
		this.lockTimestamp = null;
	}

	// Expose private properties for testing
	get isLocked() {
		return this.locked;
	}

	get lockTime() {
		return this.lockTimestamp;
	}

	setLock(locked: boolean, timestamp: number | null = null) {
		this.locked = locked;
		this.lockTimestamp = timestamp;
	}

	setDB(db: any) {
		this.db = db;
	}
}

// Mock the TranscriptionsQueue module
jest.mock('../transcriptionsQueue', () => ({
	__esModule: true,
	default: MockTranscriptionsQueue,
}));

describe('TranscriptionsQueue - updateItem and lock timeout', () => {
	let queue: MockTranscriptionsQueue;
	const sessionId = 'test-session-123';

	const mockQueueMessage: TranscriptionPartQueueMessage = {
		audioChunk: new Blob(['test audio']),
		noteId: 'note-123',
		contactId: 'contact-456',
		transcriptionId: 'transcription-789',
		sessionId: 'original-session',
		metadata: {
			partNumber: 1,
			startTime: '0',
			endTime: '1000',
			isLastPart: false,
		},
	};

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();

		// Clear singleton instances
		MockTranscriptionsQueue.clearInstances();

		// Get queue instance
		queue = MockTranscriptionsQueue.getInstance(sessionId);
	});

	afterEach(() => {
		jest.useRealTimers();
		// Clear singleton instances
		MockTranscriptionsQueue.clearInstances();
	});

	describe('updateItem', () => {
		it('should update an existing item in the queue', async () => {
			const key = 'test-key-123';
			const updatedMessage = {
				...mockQueueMessage,
				sessionId: 'new-session-456',
			};

			const result = await queue.updateItem(key, updatedMessage);

			expect(result).toBeUndefined(); // Successful update returns void
		});

		it('should handle null database during update', async () => {
			// Set database to null
			queue.setDB(null);

			const result = await queue.updateItem('key', mockQueueMessage);

			expect(result).toBeNull();
		});

		it('should handle successful update with valid database', async () => {
			// Set database to valid object
			queue.setDB({});

			const result = await queue.updateItem('test-key', mockQueueMessage);

			expect(result).toBeUndefined();
		});
	});

	describe('lock timeout functionality', () => {
		beforeEach(() => {
			// Reset lock state
			queue.setLock(false, null);
		});

		it('should set lock timestamp when acquiring lock', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			const result = await queue.peekAndLockFront();

			// Should acquire lock and set timestamp
			expect(queue.isLocked).toBe(true);
			expect(queue.lockTime).toBe(currentTime);
			expect(result).toBeNull(); // Empty queue returns null
		});

		it('should respect active lock within timeout period', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			// Set lock manually (10 seconds ago, within 30s timeout)
			queue.setLock(true, currentTime - 10000);

			const result = await queue.peekAndLockFront();

			expect(result).toBeNull();
			expect(queue.isLocked).toBe(true); // Should remain locked
		});

		it('should allow lock acquisition when timeout has expired', async () => {
			const currentTime = Date.now();
			jest.spyOn(Date, 'now').mockReturnValue(currentTime);

			// Set expired lock (35 seconds ago, beyond 30s timeout)
			queue.setLock(true, currentTime - 35000);

			const result = await queue.peekAndLockFront();

			// Should clear expired lock and acquire new one
			expect(queue.isLocked).toBe(true);
			expect(queue.lockTime).toBe(currentTime);
			expect(result).toBeNull(); // Empty queue returns null
		});

		it('should clear lock when deleteByKey is called', async () => {
			const key = 'test-key-789';

			// Set lock
			queue.setLock(true, Date.now());

			await queue.deleteByKey(key);

			expect(queue.isLocked).toBe(false);
			expect(queue.lockTime).toBeNull();
		});

		it('should clear lock when clear is called', async () => {
			// Set lock
			queue.setLock(true, Date.now());

			await queue.clear();

			expect(queue.isLocked).toBe(false);
			expect(queue.lockTime).toBeNull();
		});
	});

	describe('unlockFront', () => {
		it('should clear lock state', () => {
			// Set lock
			queue.setLock(true, Date.now());

			queue.unlockFront();

			expect(queue.isLocked).toBe(false);
			expect(queue.lockTime).toBeNull();
		});
	});

	describe('singleton pattern', () => {
		it('should return same instance for same sessionId', () => {
			const instance1 = MockTranscriptionsQueue.getInstance('same-session');
			const instance2 = MockTranscriptionsQueue.getInstance('same-session');

			expect(instance1).toBe(instance2);
		});

		it('should return different instances for different sessionIds', () => {
			const instance1 = MockTranscriptionsQueue.getInstance('session-1');
			const instance2 = MockTranscriptionsQueue.getInstance('session-2');

			expect(instance1).not.toBe(instance2);
		});
	});
});
