import { v4 as uuid } from 'uuid';

import { closeBroadcastChannel, getTabSessionId, isTabAlive, startTabCoordination } from '../sessionUtils';

// Mock uuid
jest.mock('uuid', () => ({
	v4: jest.fn(),
}));

// Mock BroadcastChannel
class MockBroadcastChannel {
	name: string;

	constructor(name: string) {
		this.name = name;
	}

	addEventListener() {
		// Mock implementation
	}

	removeEventListener() {
		// Mock implementation
	}

	postMessage() {
		// Mock implementation
	}

	close() {
		// Mock implementation
	}
}

// Setup global BroadcastChannel mock
(global as any).BroadcastChannel = MockBroadcastChannel;

// Mock sessionStorage
const createSessionStorageMock = () => {
	let store: Record<string, string> = {};

	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		length: Object.keys(store).length,
		key: (index: number) => Object.keys(store)[index] || null,
	};
};

// Setup sessionStorage mock
Object.defineProperty(window, 'sessionStorage', {
	value: createSessionStorageMock(),
	writable: true,
});

describe('sessionUtils', () => {
	const mockUuid = uuid as jest.MockedFunction<typeof uuid>;

	beforeEach(() => {
		jest.clearAllMocks();
		sessionStorage.clear();
		closeBroadcastChannel(); // Reset global channel
	});

	describe('getTabSessionId', () => {
		it('should generate new session ID if none exists', () => {
			const testUuid = 'test-uuid-123';
			mockUuid.mockReturnValue(testUuid);

			const sessionId = getTabSessionId();

			expect(sessionId).toBe(`session_${testUuid}`);
			expect(sessionStorage.getItem('transcription_tab_session_id')).toBe(`session_${testUuid}`);
		});

		it('should return existing session ID from sessionStorage', () => {
			const existingId = 'session_existing-uuid-456';
			sessionStorage.setItem('transcription_tab_session_id', existingId);

			const sessionId = getTabSessionId();

			expect(sessionId).toBe(existingId);
			expect(mockUuid).not.toHaveBeenCalled();
		});

		it('should generate new ID if sessionStorage value is empty', () => {
			sessionStorage.setItem('transcription_tab_session_id', '');
			const testUuid = 'new-uuid-789';
			mockUuid.mockReturnValue(testUuid);

			const sessionId = getTabSessionId();

			expect(sessionId).toBe(`session_${testUuid}`);
			expect(sessionStorage.getItem('transcription_tab_session_id')).toBe(`session_${testUuid}`);
		});
	});

	describe('isTabAlive', () => {
		it('should return a promise', () => {
			const targetSessionId = 'target-session-123';
			const result = isTabAlive(targetSessionId);

			expect(result).toBeInstanceOf(Promise);
		});

		it('should call uuid to generate request ID', () => {
			const targetSessionId = 'target-session-456';
			mockUuid.mockReturnValue('test-request-id');

			isTabAlive(targetSessionId);

			expect(mockUuid).toHaveBeenCalled();
		});
	});

	describe('startTabCoordination', () => {
		it('should set up message listener and return cleanup function', () => {
			const sessionId = 'test-session-789';
			const cleanup = startTabCoordination(sessionId);

			// Verify cleanup function is returned
			expect(typeof cleanup).toBe('function');

			// Verify cleanup doesn't throw
			expect(() => cleanup()).not.toThrow();
		});

		it('should clean up event listeners when cleanup function is called', () => {
			const sessionId = 'cleanup-test-session';
			const cleanup = startTabCoordination(sessionId);

			// Verify cleanup doesn't throw
			expect(() => cleanup()).not.toThrow();
		});
	});

	describe('closeBroadcastChannel', () => {
		it('should close the global broadcast channel', () => {
			// First create a channel by calling isTabAlive
			const targetSessionId = 'test-session';
			mockUuid.mockReturnValue('test-request');

			isTabAlive(targetSessionId);

			// Then close it
			expect(() => closeBroadcastChannel()).not.toThrow();
		});

		it('should handle being called when no channel exists', () => {
			expect(() => closeBroadcastChannel()).not.toThrow();
		});
	});
});
