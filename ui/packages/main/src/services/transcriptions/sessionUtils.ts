import { v4 as uuid } from 'uuid';

const TAB_SESSION_KEY = 'transcription_tab_session_id';
const BROADCAST_CHANNEL_NAME = 'transcription-coordination';

// BroadcastChannel configuration
const PING_TIMEOUT = 1000; // 1 second timeout for ping responses

export type PingMessage = {
	type: 'ping';
	targetSessionId: string;
	requestId: string;
};

export type PingResponseMessage = {
	type: 'ping-response';
	sessionId: string;
	requestId: string;
};

export type BroadcastMessage = PingMessage | PingResponseMessage;

/**
 * Generate a unique session ID for this browser tab
 */
const generateSessionId = (): string => {
	return `session_${uuid()}`;
};

/**
 * Get or create a session ID for this tab, persisted in sessionStorage
 * This ensures each tab has a consistent session ID across page refreshes
 */
export const getTabSessionId = (): string => {
	let sessionId = sessionStorage.getItem(TAB_SESSION_KEY);

	if (!sessionId) {
		sessionId = generateSessionId();
		sessionStorage.setItem(TAB_SESSION_KEY, sessionId);
	}

	return sessionId;
};

// Global BroadcastChannel instance
let broadcastChannel: BroadcastChannel | null = null;

/**
 * Get or create the BroadcastChannel instance for tab coordination
 */
const getBroadcastChannel = (): BroadcastChannel => {
	if (!broadcastChannel) {
		broadcastChannel = new BroadcastChannel(BROADCAST_CHANNEL_NAME);
	}
	return broadcastChannel;
};

/**
 * Check if a tab with the given sessionId is still alive using BroadcastChannel ping/pong
 */
export const isTabAlive = async (sessionId: string): Promise<boolean> => {
	const channel = getBroadcastChannel();
	const requestId = uuid();

	return new Promise((resolve) => {
		const timeout = setTimeout(() => {
			channel.removeEventListener('message', handler);
			resolve(false);
		}, PING_TIMEOUT);

		const handler = (event: MessageEvent<BroadcastMessage>) => {
			if (
				event.data.type === 'ping-response' &&
				event.data.sessionId === sessionId &&
				event.data.requestId === requestId
			) {
				clearTimeout(timeout);
				channel.removeEventListener('message', handler);
				resolve(true);
			}
		};

		channel.addEventListener('message', handler);

		const pingMessage: PingMessage = {
			type: 'ping',
			targetSessionId: sessionId,
			requestId,
		};

		channel.postMessage(pingMessage);
	});
};

/**
 * Start listening for ping messages and respond to them
 * This allows other tabs to check if this tab is alive
 */
export const startTabCoordination = (sessionId: string): (() => void) => {
	const channel = getBroadcastChannel();

	const messageHandler = (event: MessageEvent<BroadcastMessage>) => {
		if (event.data.type === 'ping' && event.data.targetSessionId === sessionId) {
			const responseMessage: PingResponseMessage = {
				type: 'ping-response',
				sessionId,
				requestId: event.data.requestId,
			};

			channel.postMessage(responseMessage);
		}
	};

	channel.addEventListener('message', messageHandler);

	// Return cleanup function
	return () => {
		channel.removeEventListener('message', messageHandler);
	};
};

/**
 * Close the BroadcastChannel when no longer needed
 */
export const closeBroadcastChannel = (): void => {
	if (broadcastChannel) {
		broadcastChannel.close();
		broadcastChannel = null;
	}
};
