import { useAppSelector } from 'store';
import { useLazyGetNoteTranscriptionsQuery } from 'store/api/notes/hooks';
import { selectCurrentProviderId } from 'store/api/providers/selectors';
import { useCompleteTranscriptionPartMutation } from 'store/api/transcriptions/hooks';

import { TranscriptionStateManager } from '../transcriptionStateManager';

const useCompleteTranscriptionParts = () => {
	const providerId = useAppSelector(selectCurrentProviderId);
	const [completeTranscription] = useCompleteTranscriptionPartMutation();
	const [fetchTranscriptions] = useLazyGetNoteTranscriptionsQuery();

	const completeTranscriptionParts = async ({
		transcriptionId,
		contactId,
		noteId,
	}: {
		transcriptionId: string;
		contactId: string;
		noteId: string;
	}) => {
		if (!transcriptionId || !contactId || !noteId) return;

		await completeTranscription({
			transcriptionId,
		}).unwrap();

		// Mark transcription as completed since this was the last part
		TranscriptionStateManager.markTranscriptionCompleted(providerId, transcriptionId);

		// Refetch transcriptions to get the latest status
		fetchTranscriptions({
			contactId,
			noteId,
		});
	};

	return completeTranscriptionParts;
};

export default useCompleteTranscriptionParts;
