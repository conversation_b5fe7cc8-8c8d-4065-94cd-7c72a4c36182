import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { renderHook } from '@testing-library/react';

import { logger } from '@carepatron/utilities';

import { isTabAlive, startTabCoordination } from '../../sessionUtils';
import TranscriptionsQueue from '../../transcriptionsQueue';
import { TranscriptionStateManager } from '../../transcriptionStateManager';
import useCompleteTranscriptionParts from '../useCompleteTranscriptionParts';
// Import the hook after mocking
import useTranscriptionsQueueWatcher from '../useTranscriptionsQueueWatcher';

// Mock dependencies
jest.mock('@carepatron/utilities', () => ({
	logger: {
		info: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
}));

jest.mock('../../sessionUtils', () => ({
	getTabSessionId: jest.fn(() => 'current-session-123'),
	isTabAlive: jest.fn(),
	startTabCoordination: jest.fn(() => jest.fn()),
}));

const mockQueue = {
	getInstance: jest.fn(),
	getAllItems: jest.fn(),
	updateItem: jest.fn(),
	peekAndLockFront: jest.fn(),
	deleteByKey: jest.fn(),
	unlockFront: jest.fn(),
};

jest.mock('../../transcriptionsQueue', () => ({
	__esModule: true,
	default: {
		getInstance: jest.fn(() => mockQueue),
	},
}));

jest.mock('../../transcriptionStateManager', () => ({
	TranscriptionStateManager: {
		markTranscriptionActive: jest.fn(),
	},
}));

jest.mock('../useCompleteTranscriptionParts', () => {
	return jest.fn(() => jest.fn());
});

jest.mock('store', () => ({
	useAppSelector: jest.fn(() => 'test-provider-123'),
}));

// Fix the useFeatureFlag mock to ensure it always returns the correct structure
jest.mock('store/slices/features/hooks', () => ({
	useFeatureFlag: jest.fn(() => ({ hasFeatureFlagged: false })),
}));

// Fix the services/api mock by using jest.fn() directly
jest.mock('services/api', () => ({
	default: {
		uploadTranscriptionPart: jest.fn(),
	},
}));

// Mock sessionStorage for session ID generation
const mockSessionStorage = {
	getItem: jest.fn(),
	setItem: jest.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
	value: mockSessionStorage,
	writable: true,
});

// Import modules here to access the mocks in both describe blocks
const api = require('services/api').default;
const { useFeatureFlag } = require('store/slices/features/hooks');

describe('useTranscriptionsQueueWatcher - Session-based Processing', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		// Reset all mocks to default successful states
		mockSessionStorage.getItem.mockReturnValue(null);
		mockQueue.peekAndLockFront.mockResolvedValue(null);
		mockQueue.getAllItems.mockResolvedValue([]);
		mockQueue.deleteByKey.mockResolvedValue(undefined);
		mockQueue.unlockFront.mockReturnValue(undefined);
		api.uploadTranscriptionPart.mockResolvedValue([]);

		// Ensure useFeatureFlag mock returns correct structure
		useFeatureFlag.mockReturnValue({ hasFeatureFlagged: false });
	});

	it('should integrate with the new queue API and return sessionId', async () => {
		const { result } = renderHook(() => useTranscriptionsQueueWatcher());

		expect(result.current.sessionId).toBeDefined();
		expect(typeof result.current.sessionId).toBe('string');
		expect(result.current.sessionId.length).toBeGreaterThan(0);
	});

	it('should maintain consistent sessionId across re-renders', async () => {
		const { result, rerender } = renderHook(() => useTranscriptionsQueueWatcher());
		const firstSessionId = result.current.sessionId;

		rerender();

		expect(result.current.sessionId).toBe(firstSessionId);
		expect(typeof result.current.sessionId).toBe('string');
	});

	it('should setup queue processing interval', async () => {
		jest.useFakeTimers();
		const setIntervalSpy = jest.spyOn(global, 'setInterval');

		renderHook(() => useTranscriptionsQueueWatcher());

		// Should setup the queue processing interval
		expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 3000);

		jest.useRealTimers();
		setIntervalSpy.mockRestore();
	});

	it('should setup orphan adoption check interval', async () => {
		jest.useFakeTimers();
		const setIntervalSpy = jest.spyOn(global, 'setInterval');

		renderHook(() => useTranscriptionsQueueWatcher());

		// Should setup both intervals: queue processing (3000ms) and adoption check (60000ms)
		expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 3000);
		expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 60000);

		jest.useRealTimers();
		setIntervalSpy.mockRestore();
	});

	// Basic functionality tests without complex timer manipulation
	it('should initialize with correct session ID format', async () => {
		const { result } = renderHook(() => useTranscriptionsQueueWatcher());

		// Session ID should be the mocked value
		expect(result.current.sessionId).toBe('current-session-123');
	});

	it('should properly initialize TranscriptionsQueue instance', async () => {
		renderHook(() => useTranscriptionsQueueWatcher());

		// Should get the queue instance
		expect(TranscriptionsQueue.getInstance).toHaveBeenCalled();
	});
});

describe('useTranscriptionsQueueWatcher - Orphan Adoption', () => {
	const mockTriggerCompleteParts = jest.fn();
	const currentSessionId = 'current-session-123';

	// Create a minimal store for testing
	const createTestStore = () =>
		configureStore({
			reducer: {
				test: (state = {}) => state,
			},
		});

	const wrapper = ({ children }: { children: React.ReactNode }) => (
		<Provider store={createTestStore()}>{children}</Provider>
	);

	beforeEach(() => {
		jest.clearAllMocks();
		jest.useFakeTimers();

		// Setup mocks
		(TranscriptionsQueue.getInstance as jest.Mock).mockReturnValue(mockQueue);
		(useCompleteTranscriptionParts as jest.Mock).mockReturnValue(mockTriggerCompleteParts);
		(startTabCoordination as jest.Mock).mockReturnValue(jest.fn());

		// Default mock implementations
		mockQueue.getAllItems.mockResolvedValue([]);
		mockQueue.updateItem.mockResolvedValue(undefined);
		mockQueue.peekAndLockFront.mockResolvedValue(null);
		(isTabAlive as jest.Mock).mockResolvedValue(false);

		// Ensure useFeatureFlag mock returns correct structure
		useFeatureFlag.mockReturnValue({ hasFeatureFlagged: false });
	});

	afterEach(() => {
		jest.useRealTimers();
	});

	// Helper to create mock queue items
	const createMockQueueItem = (sessionId: string, transcriptionId: string, partNumber = 1) => ({
		key: `key-${transcriptionId}-${partNumber}`,
		value: {
			sessionId,
			transcriptionId,
			contactId: 'contact-123',
			noteId: 'note-456',
			audioChunk: new Blob(['test']),
			metadata: {
				partNumber,
				startTime: 0,
				endTime: 1000,
				isLastPart: false,
			},
		},
	});

	describe('orphan adoption mechanism', () => {
		it('should adopt orphaned items from dead tabs', async () => {
			const orphanedSessionId = 'dead-session-456';
			const orphanedItem = createMockQueueItem(orphanedSessionId, 'transcription-789');

			// Mock queue with orphaned item
			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);

			// Mock tab as dead
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			// Fast-forward to trigger adoption check (1 minute interval)
			jest.advanceTimersByTime(60 * 1000);

			// Wait for async operations
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(isTabAlive).toHaveBeenCalledWith(orphanedSessionId);
			expect(TranscriptionStateManager.markTranscriptionActive).toHaveBeenCalledWith(
				'test-provider-123',
				'transcription-789'
			);
			expect(mockQueue.updateItem).toHaveBeenCalledWith(orphanedItem.key, {
				...orphanedItem.value,
				sessionId: currentSessionId,
			});
		});

		it('should not adopt items from alive tabs', async () => {
			const aliveSessionId = 'alive-session-789';
			const aliveItem = createMockQueueItem(aliveSessionId, 'transcription-456');

			mockQueue.getAllItems.mockResolvedValue([aliveItem]);

			// Mock tab as alive
			(isTabAlive as jest.Mock).mockResolvedValue(true);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(isTabAlive).toHaveBeenCalledWith(aliveSessionId);
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should not adopt items from current session', async () => {
			const currentSessionItem = createMockQueueItem(currentSessionId, 'transcription-123');

			mockQueue.getAllItems.mockResolvedValue([currentSessionItem]);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should not check if current session is alive
			expect(isTabAlive).not.toHaveBeenCalled();
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should handle multiple orphaned items', async () => {
			const orphanedItems = [
				createMockQueueItem('dead-session-1', 'transcription-1'),
				createMockQueueItem('dead-session-2', 'transcription-2'),
				createMockQueueItem(currentSessionId, 'transcription-3'), // Should be skipped
			];

			mockQueue.getAllItems.mockResolvedValue(orphanedItems);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should check alive status for orphaned sessions only
			expect(isTabAlive).toHaveBeenCalledTimes(2);
			expect(isTabAlive).toHaveBeenCalledWith('dead-session-1');
			expect(isTabAlive).toHaveBeenCalledWith('dead-session-2');

			// Should update both orphaned items
			expect(mockQueue.updateItem).toHaveBeenCalledTimes(2);
		});

		it('should handle double-check scenario where tab comes back alive', async () => {
			const sessionId = 'flaky-session-123';
			const orphanedItem = createMockQueueItem(sessionId, 'transcription-456');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);

			// First check: tab is dead, second check: tab is alive
			(isTabAlive as jest.Mock)
				.mockResolvedValueOnce(false) // Initial check finds it dead
				.mockResolvedValueOnce(true); // Double-check finds it alive

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			// Should not adopt the item since tab came back alive
			expect(mockQueue.updateItem).not.toHaveBeenCalled();
		});

		it('should handle adoption errors gracefully', async () => {
			const orphanedItem = createMockQueueItem('dead-session-456', 'transcription-789');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			// Mock update to fail
			const updateError = new Error('Update failed');
			mockQueue.updateItem.mockRejectedValue(updateError);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(logger.error).toHaveBeenCalledWith(
				'Failed to adopt orphaned item',
				expect.objectContaining({
					transcriptionId: 'transcription-789',
					error: updateError,
				})
			);
		});

		it('should handle getAllItems errors gracefully', async () => {
			const getAllItemsError = new Error('Database error');
			mockQueue.getAllItems.mockRejectedValue(getAllItemsError);

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(logger.error).toHaveBeenCalledWith(
				'Error during orphaned items adoption',
				expect.objectContaining({
					error: getAllItemsError,
				})
			);
		});

		it('should use random delays to prevent simultaneous adoption', async () => {
			const orphanedItem = createMockQueueItem('dead-session-456', 'transcription-789');

			mockQueue.getAllItems.mockResolvedValue([orphanedItem]);
			(isTabAlive as jest.Mock).mockResolvedValue(false);

			// Mock Math.random to return a specific value
			const originalRandom = Math.random;
			Math.random = jest.fn(() => 0.5); // 2.5 second delay

			renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			jest.advanceTimersByTime(60 * 1000);

			// Should not have adopted yet (still in delay)
			expect(mockQueue.updateItem).not.toHaveBeenCalled();

			// Advance past the delay
			jest.advanceTimersByTime(3000);
			await new Promise((resolve) => setTimeout(resolve, 0));

			expect(mockQueue.updateItem).toHaveBeenCalled();

			// Restore Math.random
			Math.random = originalRandom;
		});
	});

	describe('cleanup and coordination', () => {
		it('should start and cleanup tab coordination', () => {
			const mockCleanup = jest.fn();
			(startTabCoordination as jest.Mock).mockReturnValue(mockCleanup);

			const { unmount } = renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			expect(startTabCoordination).toHaveBeenCalledWith(currentSessionId);

			unmount();

			expect(mockCleanup).toHaveBeenCalled();
		});

		it('should clear intervals on unmount', () => {
			const clearIntervalSpy = jest.spyOn(global, 'clearInterval');

			const { unmount } = renderHook(() => useTranscriptionsQueueWatcher(), { wrapper });

			unmount();

			expect(clearIntervalSpy).toHaveBeenCalledTimes(2); // Queue processing + adoption intervals
		});
	});
});
