import { renderHook } from '@testing-library/react';

// Import mocked modules
import { useAppSelector } from 'store';
import { useLazyGetNoteTranscriptionsQuery } from 'store/api/notes/hooks';
import { useCompleteTranscriptionPartMutation } from 'store/api/transcriptions/hooks';

import { TranscriptionStateManager } from '../../transcriptionStateManager';
// Import the hook after mocking
import useCompleteTranscriptionParts from '../useCompleteTranscriptionParts';

// Mock dependencies
jest.mock('store', () => ({
	useAppSelector: jest.fn(),
}));

jest.mock('store/api/notes/hooks', () => ({
	useLazyGetNoteTranscriptionsQuery: jest.fn(),
}));

jest.mock('store/api/transcriptions/hooks', () => ({
	useCompleteTranscriptionPartMutation: jest.fn(),
}));

jest.mock('../../transcriptionStateManager', () => ({
	TranscriptionStateManager: {
		markTranscriptionCompleted: jest.fn(),
	},
}));

describe('useCompleteTranscriptionParts', () => {
	const mockProviderId = 'test-provider-123';
	const mockCompleteTranscription = jest.fn();
	const mockFetchTranscriptions = jest.fn();
	const mockUnwrap = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();

		// Setup mocks
		(useAppSelector as jest.Mock).mockReturnValue(mockProviderId);
		(useCompleteTranscriptionPartMutation as jest.Mock).mockReturnValue([mockCompleteTranscription]);
		(useLazyGetNoteTranscriptionsQuery as jest.Mock).mockReturnValue([mockFetchTranscriptions]);

		mockCompleteTranscription.mockReturnValue({
			unwrap: mockUnwrap,
		});
		mockUnwrap.mockResolvedValue({});
	});

	it('should complete transcription and update state', async () => {
		const { result } = renderHook(() => useCompleteTranscriptionParts());

		const params = {
			transcriptionId: 'transcription-123',
			contactId: 'contact-456',
			noteId: 'note-789',
		};

		await result.current(params);

		// Verify API call
		expect(mockCompleteTranscription).toHaveBeenCalledWith({
			transcriptionId: 'transcription-123',
		});
		expect(mockUnwrap).toHaveBeenCalled();

		// Verify state management
		expect(TranscriptionStateManager.markTranscriptionCompleted).toHaveBeenCalledWith(
			mockProviderId,
			'transcription-123'
		);

		// Verify refetch
		expect(mockFetchTranscriptions).toHaveBeenCalledWith({
			contactId: 'contact-456',
			noteId: 'note-789',
		});
	});

	it('should handle missing parameters gracefully', async () => {
		const { result } = renderHook(() => useCompleteTranscriptionParts());

		// Test with missing transcriptionId
		await result.current({
			transcriptionId: '',
			contactId: 'contact-456',
			noteId: 'note-789',
		});

		expect(mockCompleteTranscription).not.toHaveBeenCalled();
		expect(TranscriptionStateManager.markTranscriptionCompleted).not.toHaveBeenCalled();
		expect(mockFetchTranscriptions).not.toHaveBeenCalled();
	});

	it('should handle API errors', async () => {
		const { result } = renderHook(() => useCompleteTranscriptionParts());

		const apiError = new Error('API Error');
		mockUnwrap.mockRejectedValue(apiError);

		const params = {
			transcriptionId: 'transcription-123',
			contactId: 'contact-456',
			noteId: 'note-789',
		};

		await expect(result.current(params)).rejects.toThrow('API Error');

		// Verify API was called but state management was not
		expect(mockCompleteTranscription).toHaveBeenCalled();
		expect(TranscriptionStateManager.markTranscriptionCompleted).not.toHaveBeenCalled();
		expect(mockFetchTranscriptions).not.toHaveBeenCalled();
	});

	it('should handle missing contactId parameter', async () => {
		const { result } = renderHook(() => useCompleteTranscriptionParts());

		await result.current({
			transcriptionId: 'transcription-123',
			contactId: '',
			noteId: 'note-789',
		});

		expect(mockCompleteTranscription).not.toHaveBeenCalled();
	});

	it('should handle missing noteId parameter', async () => {
		const { result } = renderHook(() => useCompleteTranscriptionParts());

		await result.current({
			transcriptionId: 'transcription-123',
			contactId: 'contact-456',
			noteId: '',
		});

		expect(mockCompleteTranscription).not.toHaveBeenCalled();
	});
});
